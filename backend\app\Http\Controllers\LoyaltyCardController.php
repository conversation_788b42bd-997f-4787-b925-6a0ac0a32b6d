<?php

namespace App\Http\Controllers;

use App\Models\LoyaltyCard;
use Illuminate\Http\Request;

class LoyaltyCardController extends Controller
{
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'card_name' => 'required|string|max:255',
            'calculation_type' => 'required|in:Point-wise,Percentage-wise',
            'point_calculation_mode' => 'nullable|in:Threshold-wise',
            'points_per_threshold' => 'nullable|integer|min:1',
            'points_per_threshold_value' => 'nullable|integer|min:1',
            'threshold_method' => 'nullable|in:per-threshold,single-threshold',
            'single_threshold_amount' => 'nullable|numeric|min:0',
            'single_threshold_points' => 'nullable|integer|min:0',
            'single_threshold_percentage' => 'nullable|numeric|between:0,100',
            'single_threshold_operator' => 'nullable|in:>,>=',
            'percentage_per_threshold' => 'nullable|integer|min:1',
            'percentage_per_threshold_value' => 'nullable|numeric|between:0,100',
        ]);

        // Create the loyalty card
        $loyaltyCard = LoyaltyCard::create([
            'card_name' => $validatedData['card_name'],
            'calculation_type' => $validatedData['calculation_type'],
            'point_calculation_mode' => $validatedData['point_calculation_mode'] ?? null,
            'points_per_threshold' => $validatedData['points_per_threshold'] ?? null,
            'points_per_threshold_value' => $validatedData['points_per_threshold_value'] ?? null,
            'threshold_method' => $validatedData['threshold_method'] ?? null,
            'single_threshold_amount' => $validatedData['single_threshold_amount'] ?? null,
            'single_threshold_points' => $validatedData['single_threshold_points'] ?? null,
            'single_threshold_percentage' => $validatedData['single_threshold_percentage'] ?? null,
            'single_threshold_operator' => $validatedData['single_threshold_operator'] ?? null,
            'percentage_per_threshold' => $validatedData['percentage_per_threshold'] ?? null,
            'percentage_per_threshold_value' => $validatedData['percentage_per_threshold_value'] ?? null,
        ]);

        // Load the ranges relationship for the response
        $loyaltyCard->load('ranges');

        return response()->json(['message' => 'Loyalty card saved successfully', 'data' => $loyaltyCard], 201);
    }

    public function index()
    {
        $loyaltyCards = LoyaltyCard::with('ranges')->get();
        return response()->json($loyaltyCards);
    }

    public function show($id)
    {
        $loyaltyCard = LoyaltyCard::with('ranges')->find($id);
        if (!$loyaltyCard) {
            return response()->json(['message' => 'Loyalty card not found'], 404);
        }
        return response()->json($loyaltyCard);
    }

    public function update(Request $request, $id)
    {
        $loyaltyCard = LoyaltyCard::find($id);
        if (!$loyaltyCard) {
            return response()->json(['message' => 'Loyalty card not found'], 404);
        }

        $validatedData = $request->validate([
            'card_name' => 'required|string|max:255',
            'calculation_type' => 'required|in:Point-wise,Percentage-wise',
            'point_calculation_mode' => 'nullable|in:Threshold-wise',
            'points_per_threshold' => 'nullable|integer|min:1',
            'points_per_threshold_value' => 'nullable|integer|min:1',
            'threshold_method' => 'nullable|in:per-threshold,single-threshold',
            'single_threshold_amount' => 'nullable|numeric|min:0',
            'single_threshold_points' => 'nullable|integer|min:0',
            'single_threshold_percentage' => 'nullable|numeric|between:0,100',
            'single_threshold_operator' => 'nullable|in:>,>=',
            'percentage_per_threshold' => 'nullable|integer|min:1',
            'percentage_per_threshold_value' => 'nullable|numeric|between:0,100',
        ]);

        $loyaltyCard->update([
            'card_name' => $validatedData['card_name'],
            'calculation_type' => $validatedData['calculation_type'],
            'point_calculation_mode' => $validatedData['point_calculation_mode'] ?? null,
            'points_per_threshold' => $validatedData['points_per_threshold'] ?? null,
            'points_per_threshold_value' => $validatedData['points_per_threshold_value'] ?? null,
            'threshold_method' => $validatedData['threshold_method'] ?? null,
            'single_threshold_amount' => $validatedData['single_threshold_amount'] ?? null,
            'single_threshold_points' => $validatedData['single_threshold_points'] ?? null,
            'single_threshold_percentage' => $validatedData['single_threshold_percentage'] ?? null,
            'single_threshold_operator' => $validatedData['single_threshold_operator'] ?? null,
            'percentage_per_threshold' => $validatedData['percentage_per_threshold'] ?? null,
            'percentage_per_threshold_value' => $validatedData['percentage_per_threshold_value'] ?? null,
        ]);

        $loyaltyCard->load('ranges');

        return response()->json(['message' => 'Loyalty card updated successfully', 'data' => $loyaltyCard]);
    }

    public function destroy($id)
    {
        $loyaltyCard = LoyaltyCard::find($id);
        if (!$loyaltyCard) {
            return response()->json(['message' => 'Loyalty card not found'], 404);
        }

        // Delete associated ranges first
        $loyaltyCard->ranges()->delete();

        // Delete the loyalty card
        $loyaltyCard->delete();

        return response()->json(['message' => 'Loyalty card deleted successfully']);
    }
}
