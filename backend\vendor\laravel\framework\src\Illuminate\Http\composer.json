{"name": "illuminate/http", "description": "The Illuminate Http package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-filter": "*", "fruitcake/php-cors": "^1.3", "guzzlehttp/guzzle": "^7.8.2", "guzzlehttp/uri-template": "^1.0", "illuminate/collections": "^11.0", "illuminate/macroable": "^11.0", "illuminate/session": "^11.0", "illuminate/support": "^11.0", "symfony/http-foundation": "^7.0.3", "symfony/http-kernel": "^7.0.3", "symfony/polyfill-php83": "^1.31", "symfony/mime": "^7.0.3"}, "autoload": {"psr-4": {"Illuminate\\Http\\": ""}}, "suggest": {"ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image()."}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}