<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DiscountScheme extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'value',
        'applies_to',
        'target',
        'start_date',
        'end_date',
        'active',
        'buy_quantity',
        'free_quantity',
    ];

    protected $casts = [
        'active' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
    ];
}