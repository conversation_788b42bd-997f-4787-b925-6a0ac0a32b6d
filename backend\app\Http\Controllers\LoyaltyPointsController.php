<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Sale;
use App\Models\LoyaltyCard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class LoyaltyPointsController extends Controller
{
    public function index(Request $request)
    {
        $customers = Customer::with(['loyaltyCard.ranges'])->get();
        $data = $customers->map(function ($customer) {
            $sales = Sale::where('customer_id', $customer->id)->get();
            $invoices = \App\Models\Invoice::where('customer_id', $customer->id)->get();
            // Normalize invoices to match sales fields
            $normalizedInvoices = $invoices->map(function ($inv) {
                $inv->total = $inv->total ?? $inv->total_amount ?? 0;
                $inv->created_at = $inv->created_at ?? $inv->invoice_date;
                return $inv;
            });
            $allSales = $sales->concat($normalizedInvoices);
            $totalSale = $allSales->sum('total');
            $cards = $customer->loyalty_cards ?? collect();
            $pointsEarned = 0;
            foreach ($cards as $card) {
                $pointsEarned += $this->calculatePointsEarned($card, $allSales);
            }
            $pointsRedeemed = $customer->points_redeemed ?? 0;
            $pointsBalance = $pointsEarned - $pointsRedeemed;
            $lastVisit = $allSales->max('created_at');
            if ($lastVisit) {
                $lastVisit = Carbon::parse($lastVisit);
                $days = Carbon::now()->diffInDays($lastVisit, false);
                if (!is_int($days) || $days < 0) {
                    $days = '-';
                }
            } else {
                $days = '-';
            }

            // Prepare all card reward details
            $cardDetails = $cards->map(function ($card) use ($sales, $totalSale) {
                $reward = null;
                if ($card->calculation_type === 'Point-wise' && $card->threshold_method === 'per-threshold') {
                    $reward = $card->points_per_threshold_value . ' point' . ($card->points_per_threshold_value != 1 ? 's' : '') . ' per ' . $card->points_per_threshold . ' LKR';
                } elseif ($card->calculation_type === 'Point-wise' && $card->threshold_method === 'single-threshold') {
                    $reward = 'If sale ' . ($card->single_threshold_operator ?: '>') . ' ' . $card->single_threshold_amount . ' LKR, give ' . $card->single_threshold_points . ' points';
                } elseif ($card->calculation_type === 'Percentage-wise' && $card->threshold_method === 'per-threshold') {
                    $reward = $card->percentage_per_threshold_value . '% per ' . $card->percentage_per_threshold . ' LKR';
                } elseif ($card->calculation_type === 'Percentage-wise' && $card->threshold_method === 'single-threshold') {
                    $reward = 'If sale ' . ($card->single_threshold_operator ?: '>') . ' ' . $card->single_threshold_amount . ' LKR, give ' . $card->single_threshold_percentage . '%';
                }
                return [
                    'card_name' => $card->card_name,
                    'calculation_type' => $card->calculation_type,
                    'threshold_method' => $card->threshold_method,
                    'points_per_threshold' => $card->points_per_threshold,
                    'points_per_threshold_value' => $card->points_per_threshold_value,
                    'single_threshold_amount' => $card->single_threshold_amount,
                    'single_threshold_points' => $card->single_threshold_points,
                    'single_threshold_percentage' => $card->single_threshold_percentage,
                    'single_threshold_operator' => $card->single_threshold_operator,
                    'percentage_per_threshold' => $card->percentage_per_threshold,
                    'percentage_per_threshold_value' => $card->percentage_per_threshold_value,
                    'reward' => $reward,
                ];
            })->values();

            return [
                'id' => $customer->id,
                'customer_name' => $customer->customer_name,
                'phone' => $customer->phone,
                'nic_number' => $customer->nic_number,
                'loyalty_card_number' => $customer->loyalty_card_number,
                'card_types' => $customer->card_types,
                'cards' => $cardDetails,
                'visits' => $allSales->count(),
                'total_quantity' => 0,
                'total_sale' => $totalSale,
                'points_earned' => $pointsEarned,
                'points_redeemed' => $pointsRedeemed,
                'points_balance' => $pointsBalance,
                'last_visit' => $lastVisit ? $lastVisit->format('d-m-Y') : null,
                'days' => $days,
            ];
        });

        return response()->json(['data' => $data]);
    }

    public function recordSale(Request $request, $phone)
    {
        $validated = $request->validate([
            'total_amount' => 'required|numeric|min:0',
        ]);

        $customer = Customer::where('phone', $phone)->first();
        if (!$customer) {
            return response()->json(['error' => 'Customer not found'], 404);
        }

        DB::beginTransaction();
        try {
            $sale = Sale::create([
                'customer_id' => $customer->id,
                'total' => $validated['total_amount'], // FIX: use 'total' field
                'created_at' => Carbon::now(),
            ]);

            $totalSale = Sale::where('customer_id', $customer->id)->sum('total'); // FIX: use 'total' field
            $card = $customer->loyaltyCard;
            $pointsEarned = $this->calculatePointsEarned($card, $sales);
            $pointsRedeemed = 0; // Placeholder
            $pointsBalance = $customer->points_balance ?? ($pointsEarned - $pointsRedeemed);

            $customer->update(['points_balance' => $pointsBalance]);

            DB::commit();

            $sales = Sale::where('customer_id', $customer->id)->get();
            $lastVisit = $sales->max('created_at');
            if ($lastVisit) {
                $lastVisit = Carbon::parse($lastVisit);
                $days = Carbon::now()->diffInDays($lastVisit, false); // signed difference
                if (!is_int($days) || $days < 0) {
                    $days = '-';
                }
            } else {
                $days = '-';
            }

            return response()->json([
                'data' => [
                    'id' => $customer->id,
                    'customer_name' => $customer->customer_name,
                    'phone' => $customer->phone,
                    'nic_number' => $customer->nic_number,
                    'loyalty_card_number' => $customer->loyalty_card_number,
                    'card_name' => $card ? $card->card_name : null,
                    'card_type' => $card ? $card->calculation_type . ($card->point_calculation_mode ? " ({$card->point_calculation_mode})" : "") : "",
                    'visits' => $sales->count(),
                    'total_quantity' => 0,
                    'total_sale' => $totalSale,
                    'points_earned' => $pointsEarned,
                    'points_redeemed' => $pointsRedeemed,
                    'points_balance' => $pointsBalance,
                    'last_visit' => $lastVisit ? $lastVisit->format('d-m-Y') : null,
                    'days' => $days,
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Failed to record sale'], 500);
        }
    }

    public function calculatePointsEarned($card, $sales)
    {
        if (!$card) return 0;

        // POINT-WISE LOGIC
        if ($card->calculation_type === 'Point-wise') {
            if ($card->threshold_method === 'per-threshold' && $card->points_per_threshold && $card->points_per_threshold_value) {
                // Points per threshold
                $points = 0;
                foreach ($sales as $sale) {
                    $points += floor($sale->total / $card->points_per_threshold) * $card->points_per_threshold_value;
                }
                return $points;
            } elseif ($card->threshold_method === 'single-threshold' && $card->single_threshold_amount && $card->single_threshold_points) {
                // Single threshold logic
                $points = 0;
                foreach ($sales as $sale) {
                    $op = $card->single_threshold_operator ?: '>';
                    if (($op === '>' && $sale->total > $card->single_threshold_amount) ||
                        ($op === '>=' && $sale->total >= $card->single_threshold_amount)) {
                        $points += $card->single_threshold_points;
                    }
                }
                return $points;
            }
        }

        // PERCENTAGE-WISE LOGIC
        if ($card->calculation_type === 'Percentage-wise') {
            if ($card->threshold_method === 'per-threshold' && $card->percentage_per_threshold && $card->percentage_per_threshold_value) {
                // Percentage per threshold
                $totalPercentage = 0;
                foreach ($sales as $sale) {
                    $totalPercentage += floor($sale->total / $card->percentage_per_threshold) * $card->percentage_per_threshold_value;
                }
                return $totalPercentage;
            } elseif ($card->threshold_method === 'single-threshold' && $card->single_threshold_amount && $card->single_threshold_percentage) {
                // Single threshold logic
                $points = 0;
                foreach ($sales as $sale) {
                    $op = $card->single_threshold_operator ?: '>';
                    if (($op === '>' && $sale->total > $card->single_threshold_amount) ||
                        ($op === '>=' && $sale->total >= $card->single_threshold_amount)) {
                        $points += ($sale->total * $card->single_threshold_percentage / 100);
                    }
                }
                return $points;
            }
        }

        return 0;
    }
}