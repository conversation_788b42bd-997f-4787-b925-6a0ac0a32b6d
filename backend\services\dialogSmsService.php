<?php

class DialogSmsService
{
    private $username;
    private $password;
    private $apiUrl = "https://richcommunication.dialog.lk/api/sms/send";

    public function __construct()
    {
        // Load credentials from environment variables
        $this->username = getenv('DIALOG_SMS_API_USERNAME');
        $this->password = getenv('DIALOG_SMS_API_PASSWORD');
    }

    private function getDigest()
    {
        return md5($this->password);
    }

    private function getCreatedTimestamp()
    {
        // Format: YYYY-MM-DDThh:mm:ss in Asia/Colombo timezone
        $dt = new DateTime('now', new DateTimeZone('Asia/Colombo'));
        return $dt->format('Y-m-d\TH:i:s');
    }

    public function sendSms(array $messages)
    {
        $body = json_encode(['messages' => $messages]);

        $headers = [
            'Content-Type: application/json',
            'USER: ' . $this->username,
            'DIGEST: ' . $this->getDigest(),
            'CREATED: ' . $this->getCreatedTimestamp()
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            return ['error' => $error];
        }
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $decodedResponse = json_decode($response, true);

        if ($httpCode !== 200) {
            $errorMessage = $decodedResponse['error'] ?? 'Unknown error';
            return ['error' => "HTTP $httpCode: $errorMessage"];
        }

        return $decodedResponse;
    }
}
?>