<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DialogSmsService
{
    public function sendBatch(array $messages)
    {
        $username = env('DIALOG_SMS_API_USERNAME');
        $password = env('DIALOG_SMS_API_PASSWORD');
        $sender = env('DIALOG_SMS_SENDER_ID', 'IMSS.lk');

        // Generate required security parameters
        $created = now()->setTimezone('Asia/Colombo')->format('Y-m-d\TH:i:s');
        $digest = md5(strtolower($username) . $password . $created);

        // Validate and prepare messages according to Dialog API requirements
        $validatedMessages = [];
        foreach ($messages as $message) {
            if (empty($message['number']) || empty($message['text'])) {
                continue;
            }

            $validatedMessages[] = [
                'clientRef' => $message['clientRef'] ?? 'promo_' . uniqid(),
                'number' => $this->formatPhoneNumber($message['number']),
                'mask' => $message['mask'] ?? $sender,
                'text' => $message['text'],
                'campaignName' => $message['campaignName'] ?? 'PromotionCampaign_' . date('Ymd'),
            ];
        }

        if (empty($validatedMessages)) {
            return ['error' => 'No valid messages to send'];
        }

        $payload = ['messages' => $validatedMessages];

        try {
            $response = Http::timeout(30)
                ->retry(3, 1000)
                ->withOptions(['verify' => false]) // Disable SSL verification explicitly
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'USER' => $username,
                    'DIGEST' => $digest,
                    'CREATED' => $created,
                ])
                ->post('https://richcommunication.dialog.lk/api/sms/send', $payload);

            $responseData = $response->json();

            Log::debug('Dialog SMS API Response', [
                'status' => $response->status(),
                'response' => $responseData,
                'payload' => $payload
            ]);

            if ($response->successful()) {
                if (isset($responseData['resultCode']) && $responseData['resultCode'] === 0) {
                    return [
                        'success' => true,
                        'message' => 'SMS sent successfully',
                        'response' => $responseData
                    ];
                }
                return [
                    'error' => $responseData['resultDesc'] ?? 'API request failed',
                    'code' => $responseData['resultCode'] ?? 'unknown',
                    'response' => $responseData
                ];
            }

            return [
                'error' => 'API request failed',
                'status' => $response->status(),
                'response' => $responseData
            ];

        } catch (\Exception $e) {
            Log::error('Dialog SMS API Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return ['error' => 'Failed to connect to SMS service: ' . $e->getMessage()];
        }
    }

    private function formatPhoneNumber($number)
    {
        // Remove all non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $number);

        // Handle Sri Lankan numbers (convert 07 to 947)
        if (strlen($number) === 10 && strpos($number, '0') === 0) {
            return '94' . substr($number, 1);
        }

        // Handle numbers with country code already
        if (strlen($number) === 12 && strpos($number, '94') === 0) {
            return $number;
        }

        // Handle international format with +
        if (strpos($number, '+') === 0) {
            return substr($number, 1);
        }

        // Default case - assume it's already in correct format
        return $number;
    }
}