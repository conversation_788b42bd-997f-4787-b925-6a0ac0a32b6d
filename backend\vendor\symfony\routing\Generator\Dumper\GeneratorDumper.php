<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Routing\Generator\Dumper;

use Symfony\Component\Routing\RouteCollection;

/**
 * GeneratorDumper is the base class for all built-in generator dumpers.
 *
 * <AUTHOR> <<EMAIL>>
 */
abstract class GeneratorDumper implements GeneratorDumperInterface
{
    public function __construct(
        private RouteCollection $routes,
    ) {
    }

    public function getRoutes(): RouteCollection
    {
        return $this->routes;
    }
}
