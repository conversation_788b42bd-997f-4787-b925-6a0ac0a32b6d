<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class RemoveDefaultZeroFromProductVariantsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_variants', function (Blueprint $table) {
            // Remove default(0) and make price fields nullable
            $table->decimal('buying_cost', 15, 2)->nullable()->change();
            $table->decimal('sales_price', 15, 2)->nullable()->change();
            $table->decimal('minimum_price', 15, 2)->nullable()->change();
            $table->decimal('wholesale_price', 15, 2)->nullable()->change();
            $table->decimal('mrp', 15, 2)->nullable()->change();
            $table->decimal('minimum_stock_quantity', 15, 2)->nullable()->change();
            $table->decimal('opening_stock_quantity', 15, 2)->nullable()->change();
            $table->decimal('opening_stock_value', 15, 2)->nullable()->change();
        });
        
        // Update existing records: convert 0 values to null for price fields
        DB::statement("UPDATE product_variants SET buying_cost = NULL WHERE buying_cost = 0");
        DB::statement("UPDATE product_variants SET sales_price = NULL WHERE sales_price = 0");
        DB::statement("UPDATE product_variants SET minimum_price = NULL WHERE minimum_price = 0");
        DB::statement("UPDATE product_variants SET wholesale_price = NULL WHERE wholesale_price = 0");
        DB::statement("UPDATE product_variants SET mrp = NULL WHERE mrp = 0");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_variants', function (Blueprint $table) {
            // Restore default(0) values
            $table->decimal('buying_cost', 15, 2)->default(0)->change();
            $table->decimal('sales_price', 15, 2)->default(0)->change();
            $table->decimal('minimum_price', 15, 2)->default(0)->change();
            $table->decimal('wholesale_price', 15, 2)->default(0)->change();
            $table->decimal('mrp', 15, 2)->default(0)->change();
            $table->decimal('minimum_stock_quantity', 15, 2)->default(0)->change();
            $table->decimal('opening_stock_quantity', 15, 2)->default(0)->change();
            $table->decimal('opening_stock_value', 15, 2)->default(0)->change();
        });
    }
}
