<?php

namespace App\Http\Controllers;

use App\Models\CashRegistry;
use App\Models\CashMovement;
use App\RegisterReport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Sale;
use App\Models\SaleItem;

class RegisterController extends Controller
{
    public function getStatus(Request $request): JsonResponse
    {
        try {
            $userId = $request->input('user_id') ?? $request->query('user_id') ?? $request->header('user_id');

            if (empty($userId)) {
                return response()->json([
                    'status' => 'closed',
                    'register' => null,
                    'unclosed_previous_day' => null,
                ]);
            }

            $today = now()->format('Y-m-d');

            // Check for current day open register
            $openRegister = CashRegistry::where('user_id', $userId)
                ->where('status', 'open')
                ->whereNull('closed_at')
                ->whereDate('opened_at', $today)
                ->latest('opened_at')
                ->first();

            // Check for unclosed register from previous days
            $unclosedPreviousDay = CashRegistry::where('user_id', $userId)
                ->where('status', 'open')
                ->whereNull('closed_at')
                ->whereDate('opened_at', '<', $today)
                ->latest('opened_at')
                ->first();

            if ($openRegister) {
                $registerArray = $openRegister->toArray();
                // Add cash_on_hand field for frontend compatibility
                $registerArray['cash_on_hand'] = $openRegister->opening_balance;

                // Calculate total sales amount between opened_at and now
                $totalSales = \App\Models\Sale::where('created_at', '>=', $openRegister->opened_at)
                    ->where('created_at', '<=', now())
                    ->sum('total');

                // Calculate total sales quantity between opened_at and now
                $totalSalesQty = \App\Models\SaleItem::whereHas('sale', function ($query) use ($openRegister) {
                    $query->where('created_at', '>=', $openRegister->opened_at)
                        ->where('created_at', '<=', now());
                })->sum('quantity');

                $registerArray['total_sales'] = $totalSales;
                $registerArray['total_sales_qty'] = $totalSalesQty;
                $registerArray['opening_cash'] = $openRegister->opening_balance;
            } else {
                $registerArray = null;
            }

            $unclosedPreviousDayArray = null;
            if ($unclosedPreviousDay) {
                $unclosedPreviousDayArray = [
                    'id' => $unclosedPreviousDay->id,
                    'opened_at' => $unclosedPreviousDay->opened_at,
                    'opening_balance' => $unclosedPreviousDay->opening_balance,
                    'date' => $unclosedPreviousDay->opened_at ?
                        (is_string($unclosedPreviousDay->opened_at) ?
                            date('Y-m-d', strtotime($unclosedPreviousDay->opened_at)) :
                            $unclosedPreviousDay->opened_at->format('Y-m-d')) : null,
                ];
            }

            return response()->json([
                'status' => $openRegister ? 'open' : 'closed',
                'register' => $registerArray,
                'unclosed_previous_day' => $unclosedPreviousDayArray,
            ]);

        } catch (\Exception $e) {
            Log::error('Register status check failed: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json([
                'status' => 'error',
                'message' => 'Unable to check register status'
            ], 500);
        }
    }
    // In RegisterController.php

    public function openShift(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'terminal_id' => 'required|string|max:50',
            'opening_cash' => 'required|numeric|min:0',
            'continue_previous' => 'nullable|boolean',
            'previous_register_id' => 'nullable|integer|exists:cash_registries,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $userId = $request->input('user_id');
            $terminalId = $request->input('terminal_id');
            $openingCash = $request->input('opening_cash');
            $continuePrevious = $request->input('continue_previous', false);
            $previousRegisterId = $request->input('previous_register_id');

            $today = now()->format('Y-m-d');

            // Check for existing open register today
            $existingOpenToday = CashRegistry::where('user_id', $userId)
                ->where('status', 'open')
                ->whereNull('closed_at')
                ->whereDate('opened_at', $today)
                ->first();

            if ($existingOpenToday) {
                DB::rollBack();
                return response()->json([
                    'message' => 'You already have an open register session for today',
                    'register' => $existingOpenToday
                ], 409);
            }

            // Handle previous day unclosed register
            if ($continuePrevious && $previousRegisterId) {
                $previousRegister = CashRegistry::find($previousRegisterId);
                if ($previousRegister && $previousRegister->status === 'open') {
                    // Add to existing opening cash
                    $finalOpeningCash = $previousRegister->opening_balance + $openingCash;

                    $previousRegister->update([
                        'opening_balance' => $finalOpeningCash,
                        'opened_at' => now(), // Update to today
                    ]);

                    DB::commit();

                    return response()->json([
                        'message' => 'Register continued with additional opening cash',
                        'register' => $previousRegister->fresh(),
                        'success' => true,
                    ], 200);
                }
            }

            // Create new register
            $register = CashRegistry::create([
                'user_id' => $userId,
                'terminal_id' => $terminalId,
                'opening_balance' => $openingCash,
                'status' => 'open',
                'opened_at' => now(),
                'total_sales' => 0,
                'total_sales_qty' => 0,
            ]);

            DB::commit();

            return response()->json([
                'message' => 'Register opened successfully',
                'register' => $register,
                'success' => true,
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Register open failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to open register',
                'error' => $e->getMessage(),
                'success' => false,
            ], 500);
        }
    }

    public function updateOpeningCash(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'add_to_current' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $userId = $request->input('user_id');
            $amount = $request->input('amount');
            $addToCurrent = $request->input('add_to_current', false);

            // Find current open register
            $openRegister = CashRegistry::where('user_id', $userId)
                ->where('status', 'open')
                ->whereNull('closed_at')
                ->latest('opened_at')
                ->first();

            if (!$openRegister) {
                DB::rollBack();
                return response()->json([
                    'message' => 'No open register found',
                    'success' => false,
                ], 404);
            }

            $finalAmount = $addToCurrent ?
                $openRegister->opening_balance + $amount :
                $amount;

            $openRegister->update([
                'opening_balance' => $finalAmount,
            ]);

            DB::commit();

            return response()->json([
                'message' => $addToCurrent ?
                    'Opening cash amount added successfully' :
                    'Opening cash amount updated successfully',
                'register' => $openRegister->fresh(),
                'final_amount' => $finalAmount,
                'success' => true,
            ], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Update opening cash failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to update opening cash',
                'error' => $e->getMessage(),
                'success' => false,
            ], 500);
        }
    }

    public function closePreviousDayRegister(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'register_id' => 'required|integer|exists:cash_registries,id',
            'closing_balance' => 'required|numeric|min:0',
            'actual_cash' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $register = CashRegistry::find($request->input('register_id'));

            if (!$register || $register->status !== 'open') {
                DB::rollBack();
                return response()->json([
                    'message' => 'Register not found or already closed',
                    'success' => false,
                ], 404);
            }

            // Calculate sales data for the register period
            $totalSales = Sale::where('created_at', '>=', $register->opened_at)
                ->where('created_at', '<=', now())
                ->sum('total');

            $totalSalesQty = SaleItem::whereHas('sale', function ($query) use ($register) {
                $query->where('created_at', '>=', $register->opened_at)
                    ->where('created_at', '<=', now());
            })->sum('quantity');

            $register->update([
                'status' => 'closed',
                'closed_at' => now(),
                'closing_balance' => $request->input('closing_balance'),
                'actual_cash' => $request->input('actual_cash'),
                'total_sales' => $totalSales,
                'total_sales_qty' => $totalSalesQty,
            ]);

            // Create register report record
            $reportData = [
                'register_id' => $register->id,
                'user_id' => $register->user_id,
                'terminal_id' => $register->terminal_id,
                'status' => 'closed',
                'opened_at' => $register->opened_at,
                'closed_at' => $register->closed_at,
                'opening_balance' => $register->opening_balance,
                'closing_balance' => $request->input('closing_balance'),
                'actual_cash' => $request->input('actual_cash'),
                'total_sales' => $totalSales,
                'total_sales_qty' => $totalSalesQty,
            ];

            \App\RegisterReport::create($reportData);

            DB::commit();

            return response()->json([
                'message' => 'Previous day register closed successfully',
                'register' => $register,
                'success' => true,
            ], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Close previous day register failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to close previous day register',
                'error' => $e->getMessage(),
                'success' => false,
            ], 500);
        }
    }

    public function closeShift(Request $request): JsonResponse
    {
        \Log::info('closeShift called with input: ' . json_encode($request->all()));

        $validator = Validator::make($request->all(), [
            'register_id' => 'required|integer|exists:cash_registries,id',
            'closing_balance' => 'required|numeric|min:0',
            'actual_cash' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            \Log::error('closeShift validation failed: ' . json_encode($validator->errors()->all()));
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $register = CashRegistry::find($request->input('register_id'));

            if (!$register || $register->status !== 'open') {
                DB::rollBack();
                \Log::error('closeShift no open register found for id: ' . $request->input('register_id'));
                return response()->json([
                    'message' => 'No open register found'
                ], 404);
            }

            // Calculate sales data
            $totalSales = Sale::where('created_at', '>=', $register->opened_at)
                ->where('created_at', '<=', now())
                ->sum('total');

            $totalSalesQty = SaleItem::whereHas('sale', function ($query) use ($register) {
                $query->where('created_at', '>=', $register->opened_at)
                    ->where('created_at', '<=', now());
            })->sum('quantity');

            $register->update([
                'status' => 'closed',
                'closed_at' => now(),
                'closing_balance' => $request->input('closing_balance'),
                'actual_cash' => $request->input('actual_cash'),
                'total_sales' => $totalSales,
                'total_sales_qty' => $totalSalesQty,
            ]);

            // Create register report record
            \Log::info('Creating RegisterReport with user_id: ' . $register->user_id . ', terminal_id: ' . $register->terminal_id);

            $reportData = [
                'register_id' => $register->id,
                'user_id' => $register->user_id,
                'terminal_id' => $register->terminal_id,
                'status' => 'closed',
                'opened_at' => $register->opened_at,
                'closed_at' => $register->closed_at,
                'opening_balance' => $register->opening_balance,
                'closing_balance' => $request->input('closing_balance'),
                'actual_cash' => $request->input('actual_cash'),
                'total_sales' => $totalSales,
                'total_sales_qty' => $totalSalesQty,
            ];

            \App\RegisterReport::create($reportData);

            DB::commit();

            return response()->json([
                'message' => 'Register closed successfully',
                'register' => $register,
                'total_sales' => $totalSales,
                'total_sales_qty' => $totalSalesQty,
                'opening_cash' => $register->opening_balance,
                'closing_time' => $register->closed_at,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('closeShift failed: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json([
                'message' => 'Failed to close register',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getStoredRegisterReports(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'user_id' => 'nullable|integer|exists:users,id',
            'status' => 'nullable|in:open,closed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Fetch closed register reports from register_reports table
            $reportQuery = \App\RegisterReport::query();

            if ($request->filled('start_date')) {
                $reportQuery->whereDate('opened_at', '>=', $request->input('start_date'));
            }

            if ($request->filled('end_date')) {
                $reportQuery->whereDate('closed_at', '<=', $request->input('end_date'));
            }

            if ($request->filled('user_id')) {
                $reportQuery->where('user_id', $request->input('user_id'));
            }

            if ($request->filled('status')) {
                $reportQuery->where('status', $request->input('status'));
            } else {
                // If no status filter, include only closed reports here
                $reportQuery->where('status', 'closed');
            }

            $closedReports = $reportQuery->with('user')->get();

            // Fetch open registers from cash_registries table
            $openQuery = \App\Models\CashRegistry::query()
                ->where('status', 'open');

            if ($request->filled('start_date')) {
                $openQuery->whereDate('opened_at', '>=', $request->input('start_date'));
            }

            if ($request->filled('end_date')) {
                $openQuery->whereDate('opened_at', '<=', $request->input('end_date'));
            }

            if ($request->filled('user_id')) {
                $openQuery->where('user_id', $request->input('user_id'));
            }

            if ($request->filled('status')) {
                if ($request->input('status') !== 'open') {
                    // If status filter is not 'open', exclude open registers
                    $openQuery->whereRaw('1 = 0'); // no results
                }
            }

            $openRegisters = $openQuery->with('user')->get();

            // Map open registers to match RegisterReport structure for frontend
            $openRegistersMapped = $openRegisters->map(function ($register) {
                return [
                    'id' => 'open-' . $register->id,
                    'register_id' => $register->id,
                    'user_id' => $register->user_id,
                    'user' => $register->user,
                    'terminal_id' => $register->terminal_id,
                    'status' => 'open',
                    'opened_at' => $register->opened_at,
                    'closed_at' => null,
                    'opening_balance' => $register->opening_balance,
                    'closing_balance' => null,
                    'actual_cash' => null,
                    'total_sales' => $register->total_sales,
                    'total_sales_qty' => $register->total_sales_qty,
                ];
            });

            // Combine closed reports and open registers
            $combinedReports = $closedReports->concat($openRegistersMapped)->sortByDesc('opened_at')->values();

            return response()->json([
                'reports' => $combinedReports,
            ]);
        } catch (\Exception $e) {
            \Log::error('Get stored register reports failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to get stored register reports',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function addCash(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'register_id' => 'required|integer|exists:cash_registries,id',
            'amount' => 'required|numeric|min:0.01',
            'reason' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $register = CashRegistry::find($request->input('register_id'));

            if (!$register || $register->status !== 'open') {
                return response()->json([
                    'message' => 'No open cash registry session found'
                ], 404);
            }

            $movement = CashMovement::create([
                'registry_id' => $register->id,
                'type' => 'in',
                'amount' => $request->input('amount'),
                'reason' => $request->input('reason'),
                'created_at' => now(),
            ]);

            // Optionally update the cash registry's closing_balance or other fields if needed

            return response()->json([
                'message' => 'Cash added successfully',
                'movement' => $movement,
            ], 201);

        } catch (\Exception $e) {
            Log::error('Add cash failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to add cash',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function removeCash(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'register_id' => 'required|integer|exists:cash_registries,id',
            'amount' => 'required|numeric|min:0.01',
            'reason' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $register = CashRegistry::find($request->input('register_id'));

            if (!$register || $register->status !== 'open') {
                return response()->json([
                    'message' => 'No open cash registry session found'
                ], 404);
            }

            $movement = CashMovement::create([
                'registry_id' => $register->id,
                'type' => 'out',
                'amount' => $request->input('amount'),
                'reason' => $request->input('reason'),
                'created_at' => now(),
            ]);

            // Optionally update the cash registry's closing_balance or other fields if needed

            return response()->json([
                'message' => 'Cash removed successfully',
                'movement' => $movement,
            ], 201);

        } catch (\Exception $e) {
            Log::error('Remove cash failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to remove cash',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getCurrentRegistry(Request $request): JsonResponse
    {
        try {
            $userId = $request->user()->id;

            $openRegister = CashRegistry::where('user_id', $userId)
                ->where('status', 'open')
                ->whereNull('closed_at')
                ->latest('opened_at')
                ->first();

            if (!$openRegister) {
                return response()->json([
                    'message' => 'No open cash registry found',
                    'register' => null,
                ], 404);
            }

            return response()->json([
                'register' => $openRegister,
            ]);
        } catch (\Exception $e) {
            Log::error('Get current registry failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to get current cash registry',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getRegistryReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'user_id' => 'nullable|integer|exists:users,id',
            'status' => 'nullable|in:open,closed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $query = CashRegistry::query();

            if ($request->filled('start_date')) {
                $query->whereDate('opened_at', '>=', $request->input('start_date'));
            }

            if ($request->filled('end_date')) {
                $query->whereDate('closed_at', '<=', $request->input('end_date'));
            }

            if ($request->filled('user_id')) {
                $query->where('user_id', $request->input('user_id'));
            }

            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            $registries = $query->with('cashMovements', 'user')->get();

            return response()->json([
                'registries' => $registries,
            ]);
        } catch (\Exception $e) {
            Log::error('Get registry report failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to get registry report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getDetailedSummary($registerId): JsonResponse
    {
        try {
            $register = CashRegistry::find($registerId);
            
            if (!$register) {
                return response()->json([
                    'message' => 'Register not found',
                    'success' => false,
                ], 404);
            }

            $openedAt = $register->opened_at;
            $closedAt = $register->closed_at ?? now();

            // Get sales data for the register period
            $sales = Sale::where('created_at', '>=', $openedAt)
                ->where('created_at', '<=', $closedAt)
                ->get();

            $invoices = \App\Models\Invoice::where('created_at', '>=', $openedAt)
                ->where('created_at', '<=', $closedAt)
                ->get();

            // Payment Methods Summary with Cash Breakdown
            $paymentMethods = [];
            $cashBreakdown = [
                'received' => 0,
                'redemption' => 0,
                'total' => 0
            ];
            
            // Get loyalty redemptions for cash breakdown calculation
            $cashRedemptions = \App\Models\LoyaltyRedemption::where('redeemed_at', '>=', $openedAt)
                ->where('redeemed_at', '<=', $closedAt)
                ->whereNotNull('sale_id')
                ->get()
                ->keyBy('sale_id');
            
            $invoiceCashRedemptions = \App\Models\LoyaltyRedemption::where('redeemed_at', '>=', $openedAt)
                ->where('redeemed_at', '<=', $closedAt)
                ->whereNotNull('invoice_id')
                ->get()
                ->keyBy('invoice_id');
            
            foreach ($sales as $sale) {
                $method = strtolower($sale->payment_type ?? 'cash');
                
                if ($method === 'cash') {
                    // Check if this sale has loyalty redemptions
                    $redemptionAmount = $cashRedemptions->get($sale->id)?->redeemed_amount ?? 0;
                    $receivedAmount = $sale->total - $redemptionAmount;
                    
                    $cashBreakdown['received'] += $receivedAmount;
                    $cashBreakdown['redemption'] += $redemptionAmount;
                    $cashBreakdown['total'] += $sale->total;
                    
                    $paymentMethods[$method] = ($paymentMethods[$method] ?? 0) + $sale->total;
                } else {
                    $paymentMethods[$method] = ($paymentMethods[$method] ?? 0) + $sale->total;
                }
            }
            
            foreach ($invoices as $invoice) {
                $method = strtolower($invoice->payment_method ?? 'cash');
                
                if ($method === 'cash') {
                    // Check if this invoice has loyalty redemptions
                    $redemptionAmount = $invoiceCashRedemptions->get($invoice->id)?->redeemed_amount ?? 0;
                    $receivedAmount = $invoice->total - $redemptionAmount;
                    
                    $cashBreakdown['received'] += $receivedAmount;
                    $cashBreakdown['redemption'] += $redemptionAmount;
                    $cashBreakdown['total'] += $invoice->total;
                    
                    $paymentMethods[$method] = ($paymentMethods[$method] ?? 0) + $invoice->total;
                } else {
                    $paymentMethods[$method] = ($paymentMethods[$method] ?? 0) + $invoice->total;
                }
            }

            // Loyalty Redemptions - Fetch from loyalty_redemptions table
            $loyaltyRedemptions = [
                'total' => 0,
                'count' => 0
            ];
            
            // Get loyalty redemptions for the register period
            $redemptions = \App\Models\LoyaltyRedemption::where('redeemed_at', '>=', $openedAt)
                ->where('redeemed_at', '<=', $closedAt)
                ->get();
            
            foreach ($redemptions as $redemption) {
                $loyaltyRedemptions['total'] += $redemption->redeemed_amount;
                $loyaltyRedemptions['count']++;
            }
            
            // Debug logging
            \Log::info('Loyalty redemptions found:', [
                'register_id' => $registerId,
                'period' => [$openedAt, $closedAt],
                'redemptions_count' => $redemptions->count(),
                'total_amount' => $loyaltyRedemptions['total'],
                'cash_breakdown' => $cashBreakdown
            ]);

            // Free Items Summary
            $freeItems = [
                'total_quantity' => 0,
                'total_value' => 0
            ];

            // Get free items from sales
            $saleItems = \App\Models\SaleItem::whereHas('sale', function ($query) use ($openedAt, $closedAt) {
                $query->where('created_at', '>=', $openedAt)
                    ->where('created_at', '<=', $closedAt);
            })->get();

            foreach ($saleItems as $item) {
                if ($item->free_qty && $item->free_qty > 0) {
                    $freeItems['total_quantity'] += $item->free_qty;
                    $freeItems['total_value'] += ($item->unit_price * $item->free_qty);
                }
            }

            // Get free items from invoices
            $invoiceItems = \App\Models\InvoiceItem::whereHas('invoice', function ($query) use ($openedAt, $closedAt) {
                $query->where('created_at', '>=', $openedAt)
                    ->where('created_at', '<=', $closedAt);
            })->get();

            foreach ($invoiceItems as $item) {
                if ($item->free && $item->free > 0) {
                    $freeItems['total_quantity'] += $item->free;
                    $freeItems['total_value'] += ($item->unit_price * $item->free);
                }
            }

            // Discounts Summary
            $discounts = [
                'total' => 0,
                'count' => 0
            ];

            foreach ($sales as $sale) {
                if ($sale->discount && $sale->discount > 0) {
                    $discounts['total'] += $sale->discount;
                    $discounts['count']++;
                }
            }

            foreach ($invoices as $invoice) {
                if ($invoice->discount && $invoice->discount > 0) {
                    $discounts['total'] += $invoice->discount;
                    $discounts['count']++;
                }
            }

            // Returns Summary (if you have a returns system)
            $returns = [
                'total' => 0,
                'count' => 0
            ];

            // Check for sales returns
            $salesReturns = \App\Models\SalesReturn::where('created_at', '>=', $openedAt)
                ->where('created_at', '<=', $closedAt)
                ->where('status', 'approved')
                ->get();

            foreach ($salesReturns as $return) {
                $returns['total'] += $return->total_amount ?? 0;
                $returns['count']++;
            }

            return response()->json([
                'success' => true,
                'register_id' => $registerId,
                'period' => [
                    'opened_at' => $openedAt,
                    'closed_at' => $closedAt
                ],
                'payment_methods' => $paymentMethods,
                'cash_breakdown' => $cashBreakdown,
                'loyalty_redemptions' => $loyaltyRedemptions,
                'free_items' => $freeItems,
                'discounts' => $discounts,
                'returns' => $returns,
                'summary' => [
                    'total_sales' => $sales->sum('total') + $invoices->sum('total'),
                    'total_transactions' => $sales->count() + $invoices->count(),
                    'opening_cash' => $register->opening_balance,
                    'expected_cash' => $register->opening_balance + $sales->sum('total') + $invoices->sum('total')
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Get detailed summary failed: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to get detailed summary',
                'error' => $e->getMessage(),
                'success' => false,
            ], 500);
        }
    }
}
