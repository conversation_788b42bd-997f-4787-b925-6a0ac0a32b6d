{"name": "fruitcake/php-cors", "description": "Cross-origin resource sharing library for the Symfony HttpFoundation", "keywords": ["cors", "symfony", "laravel"], "homepage": "https://github.com/fruitcake/php-cors", "type": "library", "license": "MIT", "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barryvdh", "email": "<EMAIL>"}], "require": {"php": "^7.4|^8.0", "symfony/http-foundation": "^4.4|^5.4|^6|^7"}, "require-dev": {"phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5", "phpstan/phpstan": "^1.4"}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "autoload-dev": {"psr-4": {"Fruitcake\\Cors\\Tests\\": "tests/"}}, "scripts": {"actions": "composer test && composer analyse && composer check-style", "test": "phpunit", "analyse": "phpstan analyse src tests --level=9", "check-style": "phpcs -p --standard=PSR12 --exclude=Generic.Files.LineLength --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests", "fix-style": "phpcbf -p --standard=PSR12 --exclude=Generic.Files.LineLength --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests"}, "extra": {"branch-alias": {"dev-master": "1.2-dev"}}}