{"name": "pestphp/pest-plugin-laravel", "description": "The Pest Laravel Plugin", "keywords": ["php", "framework", "pest", "unit", "test", "testing", "laravel"], "license": "MIT", "require": {"php": "^8.2.0", "laravel/framework": "^11.39.1|^12.9.2", "pestphp/pest": "^3.8.2"}, "autoload": {"psr-4": {"Pest\\Laravel\\": "src/"}, "files": ["src/Autoload.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "require-dev": {"laravel/dusk": "^8.2.13|dev-develop", "orchestra/testbench": "^9.9.0|^10.2.1", "pestphp/pest-dev-tools": "^3.4.0"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true, "preferred-install": "dist", "allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"laravel": {"providers": ["Pest\\Laravel\\PestServiceProvider"]}, "pest": {"plugins": ["Pest\\Laravel\\Plugin"]}}, "scripts": {"lint": "pint", "test:lint": "pint --test", "test:unit": "pest --colors=always --exclude-group=options", "test:unit:options": "pest --colors=always --group=options --without-exception-handling --without-deprecation-handling", "test": ["@test:lint", "@test:unit", "@test:unit:options"]}}