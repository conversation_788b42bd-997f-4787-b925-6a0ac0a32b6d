<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('role_has_permissions', function (Blueprint $table) {
    $table->unsignedBigInteger('permission_id');
    $table->unsignedBigInteger('role_id');
    $table->string('guard_name')->default('api'); // Add this line

    $table->foreign('permission_id')
        ->references('id')
        ->on('permissions')
        ->onDelete('cascade');

    $table->foreign('role_id')
        ->references('id')
        ->on('roles')
        ->onDelete('cascade');

    $table->primary(['permission_id', 'role_id', 'guard_name']); // Update this line

    $table->engine = 'InnoDB';
    $table->charset = 'utf8mb4';
    $table->collation = 'utf8mb4_unicode_ci';
});
    }

    public function down()
    {
        Schema::dropIfExists('role_has_permissions');
    }
};
