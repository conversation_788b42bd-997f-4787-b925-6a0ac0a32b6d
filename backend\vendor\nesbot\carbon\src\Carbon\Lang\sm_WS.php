<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'first_day_of_week' => 0,
    'formats' => [
        'L' => 'DD/MM/YYYY',
    ],
    'months' => ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>i', '<PERSON><PERSON><PERSON>', 'Me', '<PERSON><PERSON>', '<PERSON><PERSON>i', '<PERSON>uso', '<PERSON><PERSON>', 'Oketo<PERSON>', 'Novema', '<PERSON><PERSON><PERSON>'],
    'months_short' => ['<PERSON>', 'Fe<PERSON>', 'Mat', 'Ape', 'Me', 'Iun', 'Iul', 'Aug', 'Set', 'Oke', 'Nov', 'Tes'],
    'weekdays' => ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>\'ana\'i'],
    'weekdays_short' => ['<PERSON><PERSON>', '<PERSON>o <PERSON>', '<PERSON><PERSON>', 'Aso Lul', 'Aso Tof', 'Aso Far', 'Aso To\''],
    'weekdays_min' => ['Aso Sa', 'Aso Gaf', 'Aso Lua', 'Aso Lul', 'Aso Tof', 'Aso Far', 'Aso To\''],

    'hour' => ':count uati', // less reliable
    'h' => ':count uati', // less reliable
    'a_hour' => ':count uati', // less reliable

    'minute' => ':count itiiti', // less reliable
    'min' => ':count itiiti', // less reliable
    'a_minute' => ':count itiiti', // less reliable

    'second' => ':count lua', // less reliable
    's' => ':count lua', // less reliable
    'a_second' => ':count lua', // less reliable

    'year' => ':count tausaga',
    'y' => ':count tausaga',
    'a_year' => ':count tausaga',

    'month' => ':count māsina',
    'm' => ':count māsina',
    'a_month' => ':count māsina',

    'week' => ':count vaiaso',
    'w' => ':count vaiaso',
    'a_week' => ':count vaiaso',

    'day' => ':count aso',
    'd' => ':count aso',
    'a_day' => ':count aso',
]);
