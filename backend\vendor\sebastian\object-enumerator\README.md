[![Latest Stable Version](https://poser.pugx.org/sebastian/object-enumerator/v/stable.png)](https://packagist.org/packages/sebastian/object-enumerator)
[![CI Status](https://github.com/sebastian<PERSON>mann/object-enumerator/workflows/CI/badge.svg)](https://github.com/sebastianbergmann/object-enumerator/actions)
[![codecov](https://codecov.io/gh/sebas<PERSON><PERSON><PERSON>/object-enumerator/branch/main/graph/badge.svg)](https://codecov.io/gh/sebastianbergmann/object-enumerator)

# sebastian/object-enumerator

Traverses array structures and object graphs to enumerate all referenced objects.

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

```
composer require sebastian/object-enumerator
```

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

```
composer require --dev sebastian/object-enumerator
```
