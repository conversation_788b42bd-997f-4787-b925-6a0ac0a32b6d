<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Clock;

use DateTimeImmutable;
use DateTimeZone;

final class <PERSON>ozenClock implements Clock
{
    public function __construct(private DateTimeImmutable $now)
    {
    }

    public static function fromUTC(): self
    {
        return new self(new DateTimeImmutable('now', new DateTimeZone('UTC')));
    }

    public function setTo(DateTimeImmutable $now): void
    {
        $this->now = $now;
    }

    public function now(): DateTimeImmutable
    {
        return $this->now;
    }
}
