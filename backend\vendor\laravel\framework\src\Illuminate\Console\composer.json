{"name": "illuminate/console", "description": "The Illuminate Console package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-mbstring": "*", "illuminate/collections": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0", "illuminate/view": "^11.0", "laravel/prompts": "^0.1.20|^0.2|^0.3", "nunomaduro/termwind": "^2.0", "symfony/console": "^7.0.3", "symfony/polyfill-php83": "^1.31", "symfony/process": "^7.0.3"}, "autoload": {"psr-4": {"Illuminate\\Console\\": ""}}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "suggest": {"ext-pcntl": "Required to use signal trapping.", "dragonmantank/cron-expression": "Required to use scheduler (^3.3.2).", "guzzlehttp/guzzle": "Required to use the ping methods on schedules (^7.8).", "illuminate/bus": "Required to use the scheduled job dispatcher (^11.0).", "illuminate/container": "Required to use the scheduler (^11.0).", "illuminate/filesystem": "Required to use the generator command (^11.0).", "illuminate/queue": "Required to use closures for scheduled jobs (^11.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}