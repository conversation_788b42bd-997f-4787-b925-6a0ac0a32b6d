<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use Ty<PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');

        try {
            if (!$token = JWTAuth::attempt($credentials)) {
                return response()->json(['message' => 'Invalid credentials'], 401);
            }
        } catch (JWTException $e) {
            Log::error('JWTAuth error', ['error' => $e->getMessage()]);
            return response()->json(['message' => 'Could not create token'], 500);
        }

        $user = Auth::user();
        Log::info('User logged in', ['email' => $user->email, 'roles' => $user->getRoleNames()]);
        
        // Log user activity
        \App\Http\Controllers\UserActivityController::logActivity(
            $user->id,
            'User Login',
            'Authentication',
            null,
            ['email' => $user->email, 'roles' => $user->getRoleNames()]
        );

        return response()->json([
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'role' => $user->role,
                'roles' => $user->getRoleNames(),
                'permissions' => $user->getAllPermissions()->pluck('name'),
                'is_active' => $user->is_active,
            ],
            'access_token' => $token, // Changed from 'token' to 'access_token'
        ]);
    }

    public function me(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        return response()->json([
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'role' => $user->role,
                'roles' => $user->getRoleNames(),
                'permissions' => $user->getAllPermissions()->pluck('name'),
                'is_active' => $user->is_active,
            ],
        ]);
    }

    public function logout(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Log user activity before logout
            if ($user) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $user->id,
                    'User Logout',
                    'Authentication',
                    null,
                    ['email' => $user->email]
                );
                Log::info('User logout activity logged', ['user_id' => $user->id, 'email' => $user->email]);
            } else {
                Log::warning('No authenticated user found during logout');
            }
            
            JWTAuth::invalidate(JWTAuth::getToken());
            return response()->json(['message' => 'Logged out']);
        } catch (JWTException $e) {
            Log::error('JWTAuth logout error', ['error' => $e->getMessage()]);
            return response()->json(['message' => 'Failed to logout', 'error' => $e->getMessage()], 500);
        }
    }

    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8',
            'username' => 'required|unique:users',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'username' => $request->username,
            'password' => Hash::make($request->password),
            'role' => 'user',
            'status' => 'active',
            'is_active' => true,
        ]);

        // Explicitly assign the 'user' role to ensure permissions are set
        $user->assignRole('user');

        // Reload user to ensure roles/permissions are available
        $user = User::find($user->id);

        try {
            $token = JWTAuth::fromUser($user);
        } catch (JWTException $e) {
            Log::error('JWTAuth register error', ['error' => $e->getMessage()]);
            return response()->json(['message' => 'Could not create token'], 500);
        }

        return response()->json([
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'role' => $user->role,
                'roles' => $user->getRoleNames(),
                'permissions' => $user->getAllPermissions()->pluck('name'),
                'is_active' => $user->is_active, // Ensure is_active is included
            ],
            'access_token' => $token, // Changed from 'token' to 'access_token'
        ], 201);
    }

    public function refreshToken(Request $request)
    {
        try {
            $newToken = JWTAuth::refresh();
            return response()->json(['access_token' => $newToken]);
        } catch (JWTException $e) {
            Log::error('JWTAuth refresh error', ['error' => $e->getMessage()]);
            return response()->json(['message' => 'Could not refresh token'], 401);
        }
    }

    public function verifyToken(Request $request)
    {
        try {
            $user = JWTAuth::parseToken()->authenticate();
            return response()->json(['valid' => true, 'user' => $user]);
        } catch (JWTException $e) {
            Log::error('JWTAuth verify error', ['error' => $e->getMessage()]);
            return response()->json(['valid' => false, 'message' => 'Invalid token'], 401);
        }
    }
}