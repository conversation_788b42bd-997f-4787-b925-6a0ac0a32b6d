mongodb:
    image: 'mongodb/mongodb-atlas-local:latest'
    environment:
        - MONGODB_INITDB_ROOT_USERNAME=${MONGODB_USERNAME:-}
        - MONGODB_INITDB_ROOT_PASSWORD=${MONGODB_PASSWORD:-}
    volumes:
        - 'sail-mongodb:/data/db'
    ports:
        - '${FORWARD_MONGODB_PORT:-27017}:27017'
    networks:
        - sail
    healthcheck:
        test:
            - CMD
            - mongosh
            - 'mongodb://localhost:27017/admin'
            - '--eval=db.runCommand({ping:1})'
        retries: 3
        timeout: 5s
