<?php

namespace App\Http\Controllers;

use App\Models\AccountSubGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AccountSubGroupController extends Controller
{
    public function index(Request $request)
    {
        $query = AccountSubGroup::query();

        // Filter by main group if provided
        if ($request->has('main_group')) {
            $query->where('main_group', $request->main_group);
        }

        $subGroups = $query->orderBy('main_group')->orderBy('sub_group_name')->get();

        return response()->json([
            'success' => true,
            'data' => $subGroups,
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sub_group_name' => 'required|string|max:255',
            'main_group' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Check if sub group already exists for this main group
        $exists = AccountSubGroup::where('sub_group_name', $request->sub_group_name)
            ->where('main_group', $request->main_group)
            ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'Sub group already exists for this main group',
            ], 422);
        }

        $subGroup = AccountSubGroup::create([
            'sub_group_name' => $request->sub_group_name,
            'main_group' => $request->main_group,
        ]);

        return response()->json([
            'success' => true,
            'data' => $subGroup,
            'message' => 'Sub group created successfully',
        ], 201);
    }

    public function show($id)
    {
        $subGroup = AccountSubGroup::find($id);

        if (!$subGroup) {
            return response()->json([
                'success' => false,
                'message' => 'Sub group not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $subGroup,
        ]);
    }

    public function update(Request $request, $id)
    {
        $subGroup = AccountSubGroup::find($id);

        if (!$subGroup) {
            return response()->json([
                'success' => false,
                'message' => 'Sub group not found',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'sub_group_name' => 'required|string|max:255',
            'main_group' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Check if sub group already exists for this main group (excluding current record)
        $exists = AccountSubGroup::where('sub_group_name', $request->sub_group_name)
            ->where('main_group', $request->main_group)
            ->where('id', '!=', $id)
            ->exists();

        if ($exists) {
            return response()->json([
                'success' => false,
                'message' => 'Sub group already exists for this main group',
            ], 422);
        }

        $subGroup->update([
            'sub_group_name' => $request->sub_group_name,
            'main_group' => $request->main_group,
        ]);

        return response()->json([
            'success' => true,
            'data' => $subGroup,
            'message' => 'Sub group updated successfully',
        ]);
    }

    public function destroy($id)
    {
        $subGroup = AccountSubGroup::find($id);

        if (!$subGroup) {
            return response()->json([
                'success' => false,
                'message' => 'Sub group not found',
            ], 404);
        }

        $subGroup->delete();

        return response()->json([
            'success' => true,
            'message' => 'Sub group deleted successfully',
        ]);
    }

    public function bulkDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:account_sub_groups,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        AccountSubGroup::whereIn('id', $request->ids)->delete();

        return response()->json([
            'success' => true,
            'message' => 'Selected sub groups deleted successfully',
        ]);
    }
}
