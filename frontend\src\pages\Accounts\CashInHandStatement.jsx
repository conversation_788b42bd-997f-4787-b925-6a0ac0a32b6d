import React, { useState } from 'react';
import axios from 'axios';

const CashInHandStatement = () => {
  const [fromDate, setFromDate] = useState(`${new Date().getFullYear()}-01-01`);
  const [toDate, setToDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [statement, setStatement] = useState(null);
  const [showTable, setShowTable] = useState(false);

  const handleGenerate = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setShowTable(false);
    try {
      const res = await axios.post('/api/cash-in-hand-statement', {
        from_date: fromDate,
        to_date: toDate,
      });
      setStatement(res.data);
      setShowTable(true);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch statement');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setFromDate(`${new Date().getFullYear()}-01-01`);
    setToDate(new Date().toISOString().split('T')[0]);
    setStatement(null);
    setShowTable(false);
    setError('');
  };

  const handlePrint = () => {
    window.print();
  };

  // Calculate totals if statement exists
  const totalIncome = statement ? statement.income.length : 0;
  const totalExpense = statement ? statement.expense.length : 0;
  const totalTransactions = totalIncome + totalExpense;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container px-3 py-4 mx-auto sm:px-4 md:px-6 lg:px-8 max-w-7xl">
        <h1 className="mb-6 text-2xl font-bold text-center text-gray-800 sm:mb-8 sm:text-3xl md:text-3xl lg:text-3xl xl:mb-10">
          Cash In Hand Statement
        </h1>

        {error && (
          <div className="p-4 mb-4 text-red-700 bg-red-100 border border-red-400 rounded-lg">
            {error}
          </div>
        )}

        <form className="grid grid-cols-1 gap-4 p-4 mb-6 bg-white rounded-lg shadow-lg sm:gap-6 sm:p-6 sm:mb-8 md:grid-cols-2 lg:gap-8 lg:p-8 xl:grid-cols-4 2xl:gap-10 sm:w-[100%]">
          <div className="md:col-span-1">
            <label className="block mb-2 text-sm font-medium text-gray-700 sm:mb-3 md:text-base">From Date</label>
            <input
              type="date"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
              className="block w-full px-4 py-3 text-sm transition-all duration-200 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 sm:text-base md:py-4 lg:text-lg"
            />
          </div>

          <div className="md:col-span-1">
            <label className="block mb-2 text-sm font-medium text-gray-700 sm:mb-3 md:text-base">To Date</label>
            <input
              type="date"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
              className="block w-full px-4 py-3 text-sm transition-all duration-200 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 sm:text-base md:py-4 lg:text-lg"
            />
          </div>

          <div className="flex flex-col items-stretch col-span-1 space-y-4 md:col-span-2 xl:col-span-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4 lg:space-x-6">
            <button
              type="button"
              onClick={handleReset}
              className="w-full px-6 py-3 text-sm font-medium text-white transition-all duration-200 bg-gray-600 rounded-lg shadow-md hover:bg-gray-700 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:w-auto md:px-8 md:py-4 md:text-base lg:text-md"
            >
              Reset
            </button>
            <button
              type="submit"
              onClick={handleGenerate}
              disabled={loading}
              className="w-full px-6 py-3 text-sm font-medium text-white transition-all duration-200 bg-indigo-600 rounded-lg shadow-md hover:bg-indigo-700 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-md sm:w-auto md:px-8 md:py-4 md:text-base lg:text-md"
            >
              {loading ? 'Generating...' : 'Generate Statement'}
            </button>
            {showTable && (
              <button
                type="button"
                onClick={handlePrint}
                className="w-full px-6 py-3 text-sm font-medium text-white transition-all duration-200 bg-green-600 rounded-lg shadow-md hover:bg-green-700 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:w-auto md:px-8 md:py-4 md:text-base lg:text-md"
              >
                Print Statement
              </button>
            )}
          </div>
        </form>

        {showTable && statement && (
          <>
            {/* Desktop Table View */}
            <div className="hidden overflow-hidden bg-white border border-gray-200 shadow-lg rounded-xl md:block lg:shadow-xl">
              <div className="p-6 bg-gradient-to-r from-gray-50 to-gray-100">
                <h2 className="text-xl font-bold text-gray-800">Cash In Hand Summary</h2>
                <div className="grid grid-cols-3 gap-6 mt-4">
                  <div className="p-4 bg-white rounded-lg shadow-sm">
                    <p className="text-sm font-medium text-gray-600">Opening Balance</p>
                    <p className="text-2xl font-bold text-blue-600">Rs {parseFloat(statement.opening_balance).toLocaleString()}</p>
                  </div>
                  <div className="p-4 bg-white rounded-lg shadow-sm">
                    <p className="text-sm font-medium text-gray-600">Total Income</p>
                    <p className="text-2xl font-bold text-green-600">Rs {parseFloat(statement.total_income).toLocaleString()}</p>
                  </div>
                  <div className="p-4 bg-white rounded-lg shadow-sm">
                    <p className="text-sm font-medium text-gray-600">Total Expense</p>
                    <p className="text-2xl font-bold text-red-600">Rs {parseFloat(statement.total_expense).toLocaleString()}</p>
                  </div>
                </div>
                <div className="p-4 mt-4 bg-white rounded-lg shadow-sm">
                  <p className="text-sm font-medium text-gray-600">Closing Balance</p>
                  <p className="text-3xl font-bold text-indigo-600">Rs {parseFloat(statement.balance).toLocaleString()}</p>
                </div>
              </div>

              <div className="overflow-x-auto">
                <div className="p-6">
                  <h3 className="mb-4 text-lg font-bold text-green-700">Income Transactions</h3>
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-xs font-semibold tracking-wider text-left text-gray-600 uppercase md:px-6 lg:py-4 lg:text-sm">Date</th>
                        <th scope="col" className="px-4 py-3 text-xs font-semibold tracking-wider text-left text-gray-600 uppercase md:px-6 lg:py-4 lg:text-sm">Type</th>
                        <th scope="col" className="px-4 py-3 text-xs font-semibold tracking-wider text-left text-gray-600 uppercase md:px-6 lg:py-4 lg:text-sm">Description</th>
                        <th scope="col" className="px-4 py-3 text-xs font-semibold tracking-wider text-right text-gray-600 uppercase md:px-6 lg:py-4 lg:text-sm">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {statement.income.length === 0 ? (
                        <tr>
                          <td colSpan="4" className="px-4 py-4 text-sm text-center text-gray-500 md:px-6 lg:py-5">
                            No income transactions found
                          </td>
                        </tr>
                      ) : (
                        statement.income.map((item, idx) => (
                          <tr key={`income-${idx}`} className="hover:bg-gray-50">
                            <td className="px-4 py-4 text-sm text-gray-700 whitespace-nowrap md:px-6 lg:py-5 lg:text-base">{item.date}</td>
                            <td className="px-4 py-4 text-sm text-gray-700 whitespace-nowrap md:px-6 lg:py-5 lg:text-base">
                              <span className="inline-flex px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">
                                {item.type}
                              </span>
                            </td>
                            <td className="px-4 py-4 text-sm text-gray-700 md:px-6 lg:py-5 lg:text-base">{item.description}</td>
                            <td className="px-4 py-4 text-sm font-medium text-right text-green-600 whitespace-nowrap md:px-6 lg:py-5 lg:text-base">
                              Rs {parseFloat(item.amount).toLocaleString()}
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>

                <div className="p-6 border-t border-gray-200">
                  <h3 className="mb-4 text-lg font-bold text-red-700">Expense Transactions</h3>
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-xs font-semibold tracking-wider text-left text-gray-600 uppercase md:px-6 lg:py-4 lg:text-sm">Date</th>
                        <th scope="col" className="px-4 py-3 text-xs font-semibold tracking-wider text-left text-gray-600 uppercase md:px-6 lg:py-4 lg:text-sm">Type</th>
                        <th scope="col" className="px-4 py-3 text-xs font-semibold tracking-wider text-left text-gray-600 uppercase md:px-6 lg:py-4 lg:text-sm">Description</th>
                        <th scope="col" className="px-4 py-3 text-xs font-semibold tracking-wider text-right text-gray-600 uppercase md:px-6 lg:py-4 lg:text-sm">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {statement.expense.length === 0 ? (
                        <tr>
                          <td colSpan="4" className="px-4 py-4 text-sm text-center text-gray-500 md:px-6 lg:py-5">
                            No expense transactions found
                          </td>
                        </tr>
                      ) : (
                        statement.expense.map((item, idx) => (
                          <tr key={`expense-${idx}`} className="hover:bg-gray-50">
                            <td className="px-4 py-4 text-sm text-gray-700 whitespace-nowrap md:px-6 lg:py-5 lg:text-base">{item.date}</td>
                            <td className="px-4 py-4 text-sm text-gray-700 whitespace-nowrap md:px-6 lg:py-5 lg:text-base">
                              <span className="inline-flex px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">
                                {item.type}
                              </span>
                            </td>
                            <td className="px-4 py-4 text-sm text-gray-700 md:px-6 lg:py-5 lg:text-base">{item.description}</td>
                            <td className="px-4 py-4 text-sm font-medium text-right text-red-600 whitespace-nowrap md:px-6 lg:py-5 lg:text-base">
                              Rs {parseFloat(item.amount).toLocaleString()}
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Mobile Card View */}
            <div className="space-y-4 md:hidden">
              <div className="p-6 bg-white border border-gray-200 rounded-lg shadow-md">
                <h2 className="text-lg font-bold text-gray-800">Cash Summary</h2>
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div className="p-3 rounded-lg bg-blue-50">
                    <p className="text-xs font-medium text-gray-600">Opening</p>
                    <p className="text-lg font-bold text-blue-600">Rs {parseFloat(statement.opening_balance).toLocaleString()}</p>
                  </div>
                  <div className="p-3 rounded-lg bg-green-50">
                    <p className="text-xs font-medium text-gray-600">Income</p>
                    <p className="text-lg font-bold text-green-600">Rs {parseFloat(statement.total_income).toLocaleString()}</p>
                  </div>
                  <div className="p-3 rounded-lg bg-red-50">
                    <p className="text-xs font-medium text-gray-600">Expense</p>
                    <p className="text-lg font-bold text-red-600">Rs {parseFloat(statement.total_expense).toLocaleString()}</p>
                  </div>
                  <div className="p-3 rounded-lg bg-indigo-50">
                    <p className="text-xs font-medium text-gray-600">Closing</p>
                    <p className="text-lg font-bold text-indigo-600">Rs {parseFloat(statement.balance).toLocaleString()}</p>
                  </div>
                </div>
              </div>

              <div className="p-5 bg-white border border-gray-200 rounded-lg shadow-md">
                <h3 className="mb-3 text-lg font-bold text-green-700">Income</h3>
                {statement.income.length === 0 ? (
                  <p className="text-sm text-gray-500">No income transactions</p>
                ) : (
                  <div className="space-y-4">
                    {statement.income.map((item, idx) => (
                      <div key={`mobile-income-${idx}`} className="p-3 border border-gray-100 rounded-lg">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-700">{item.date}</span>
                          <span className="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">
                            {item.type}
                          </span>
                        </div>
                        <p className="mt-1 text-sm text-gray-600">{item.description}</p>
                        <p className="mt-2 text-base font-bold text-right text-green-600">
                          Rs {parseFloat(item.amount).toLocaleString()}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="p-5 bg-white border border-gray-200 rounded-lg shadow-md">
                <h3 className="mb-3 text-lg font-bold text-red-700">Expense</h3>
                {statement.expense.length === 0 ? (
                  <p className="text-sm text-gray-500">No expense transactions</p>
                ) : (
                  <div className="space-y-4">
                    {statement.expense.map((item, idx) => (
                      <div key={`mobile-expense-${idx}`} className="p-3 border border-gray-100 rounded-lg">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-700">{item.date}</span>
                          <span className="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">
                            {item.type}
                          </span>
                        </div>
                        <p className="mt-1 text-sm text-gray-600">{item.description}</p>
                        <p className="mt-2 text-base font-bold text-right text-red-600">
                          Rs {parseFloat(item.amount).toLocaleString()}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CashInHandStatement;