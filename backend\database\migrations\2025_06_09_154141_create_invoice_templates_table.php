
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvoiceTemplatesTable extends Migration
{
    public function up()
    {
        Schema::create('invoice_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name');
            $table->string('type');
            $table->float('width');
            $table->float('height');
            $table->json('text_elements')->nullable();
            $table->json('text_positions')->nullable();
            $table->json('text_styles')->nullable();
            $table->json('text_sizes')->nullable();
            $table->json('image_elements')->nullable();
            $table->json('image_positions')->nullable();
            $table->json('image_sizes')->nullable();
            $table->json('placeholder_elements')->nullable();
            $table->json('placeholder_positions')->nullable();
            $table->json('placeholder_sizes')->nullable();
            $table->json('item_list_columns')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('invoice_templates');
    }
}
