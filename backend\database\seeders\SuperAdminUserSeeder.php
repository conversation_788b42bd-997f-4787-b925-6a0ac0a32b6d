<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class SuperAdminUserSeeder extends Seeder
{
    public function run()
    {
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'username' => 'superadmin',
                'password' => Hash::make('superadmin@123'),
                'role' => 'superadmin',
                'status' => 'active',
                'photo' => null,
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
                'is_active' => true,
            ]
        );

        $role = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'superadmin', 'guard_name' => 'api']);
        DB::table('model_has_roles')->updateOrInsert(
            [
                'model_id' => $user->id,
                'model_type' => get_class($user),
                'role_id' => $role->id
            ],
            [
                'model_id' => $user->id,
                'model_type' => get_class($user),
                'role_id' => $role->id
            ]
        );
        Log::info('Superadmin user seeded', ['email' => $user->email]);
    }
}