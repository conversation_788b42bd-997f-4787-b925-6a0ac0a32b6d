<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddBatchTrackingToSalesReturnItemsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sales_return_items', function (Blueprint $table) {
            // Add batch tracking fields to match sale_items and invoice_items
            $table->unsignedBigInteger('product_variant_id')->nullable()->after('product_id');
            $table->string('batch_number')->nullable()->after('product_variant_id');
            $table->date('expiry_date')->nullable()->after('batch_number');
            
            // Add foreign key constraint to product_variants table
            $table->foreign('product_variant_id')->references('product_variant_id')->on('product_variants')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sales_return_items', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['product_variant_id']);
            
            // Drop the batch tracking columns
            $table->dropColumn(['product_variant_id', 'batch_number', 'expiry_date']);
        });
    }
}
