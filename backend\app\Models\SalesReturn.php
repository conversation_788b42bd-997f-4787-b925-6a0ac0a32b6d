<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SalesReturn extends Model
{
    use HasFactory;

    protected $fillable = [
        'sales_return_number',
        'invoice_no',
        'bill_number',
        'customer_name',
        'refund_method',
        'remarks',
        'status',
    ];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_no', 'invoice_no');
    }

    public function sale()
    {
        return $this->belongsTo(Sale::class, 'bill_number', 'bill_number');
    }

    public function items()
    {
        return $this->hasMany(SalesReturnItem::class);
    }

    // Auto-generate incremental sales return number
    public static function generateSalesReturnNumber()
    {
        $lastReturn = self::orderBy('id', 'desc')->first();
        if (!$lastReturn) {
            return 'SR000001';
        }
        $lastNumber = $lastReturn->sales_return_number;
        // Extract numeric part
        $number = intval(substr($lastNumber, 2));
        $newNumber = $number + 1;
        return 'SR' . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }
}