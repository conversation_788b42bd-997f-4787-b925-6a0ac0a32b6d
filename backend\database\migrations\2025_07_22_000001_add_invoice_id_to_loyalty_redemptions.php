<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::table('loyalty_redemptions', function (Blueprint $table) {
            $table->unsignedBigInteger('invoice_id')->nullable()->after('sale_id');
            $table->foreign('invoice_id')->references('id')->on('invoices')->onDelete('set null');
        });
    }
    public function down()
    {
        Schema::table('loyalty_redemptions', function (Blueprint $table) {
            $table->dropForeign(['invoice_id']);
            $table->dropColumn('invoice_id');
        });
    }
}; 