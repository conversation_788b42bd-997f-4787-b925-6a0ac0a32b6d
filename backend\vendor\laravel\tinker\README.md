<p align="center"><img width="321" height="80" src="/art/logo.svg" alt="Logo Laravel Tinker"></p>

<p align="center">
<a href="https://github.com/laravel/tinker/actions"><img src="https://github.com/laravel/tinker/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/tinker"><img src="https://img.shields.io/packagist/dt/laravel/tinker" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/tinker"><img src="https://img.shields.io/packagist/v/laravel/tinker" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/tinker"><img src="https://img.shields.io/packagist/l/laravel/tinker" alt="License"></a>
</p>

## Introduction

Laravel Tinker is a powerful REPL for the Laravel framework.

## Official Documentation

Documentation for Tinker can be found on the [Laravel website](https://laravel.com/docs/artisan#tinker).

## Contributing

Thank you for considering contributing to Tinker! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

Please review [our security policy](https://github.com/laravel/tinker/security/policy) on how to report security vulnerabilities.

## License

Laravel Tinker is open-sourced software licensed under the [MIT license](LICENSE.md).
