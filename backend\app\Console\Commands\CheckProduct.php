<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;

class CheckProduct extends Command
{
    protected $signature = 'check:product {name?}';
    protected $description = 'Check product details by name or list all products';

    public function handle()
    {
        $productName = $this->argument('name');

        if (!$productName) {
            // List all products
            $products = Product::all(['product_id', 'product_name']);
            $this->info("All products in database:");
            foreach ($products as $product) {
                $this->line("ID: {$product->product_id}, Name: '{$product->product_name}'");
            }
            return;
        }

        // Search for specific product
        $this->info("Searching for product: '{$productName}'");

        // Exact match
        $exactMatch = Product::where('product_name', $productName)->first();
        if ($exactMatch) {
            $this->info("✓ Exact match found:");
            $this->line("  ID: {$exactMatch->product_id}, Name: '{$exactMatch->product_name}'");
        } else {
            $this->warn("✗ No exact match found");
        }

        // Case-insensitive match
        $caseInsensitiveMatch = Product::whereRaw('LOWER(TRIM(product_name)) = LOWER(?)', [trim($productName)])->first();
        if ($caseInsensitiveMatch) {
            $this->info("✓ Case-insensitive match found:");
            $this->line("  ID: {$caseInsensitiveMatch->product_id}, Name: '{$caseInsensitiveMatch->product_name}'");
        } else {
            $this->warn("✗ No case-insensitive match found");
        }

        // Similar matches (contains)
        $similarMatches = Product::where('product_name', 'LIKE', "%{$productName}%")->get();
        if ($similarMatches->count() > 0) {
            $this->info("Similar matches found:");
            foreach ($similarMatches as $match) {
                $this->line("  ID: {$match->product_id}, Name: '{$match->product_name}'");
            }
        } else {
            $this->warn("✗ No similar matches found");
        }
    }
}
