<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\InvoiceTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Http\Resources\InvoiceTemplateResource;

class InvoiceTemplateController extends Controller
{
    public function index()
    {
        try {
            $templates = InvoiceTemplate::all();
            return response()->json(['data' => $templates], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching templates: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch templates'], 500);
        }
    }

    public function debugList()
    {
        try {
            $templates = InvoiceTemplate::select('id', 'name', 'is_default')->get();
            return response()->json(['data' => $templates], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching debug templates: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch debug templates'], 500);
        }
    }

    public function setDefault(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            InvoiceTemplate::where('is_default', true)->update(['is_default' => false]);
            $template = InvoiceTemplate::findOrFail($id);
            $template->is_default = true;
            $template->save();
            DB::commit();
            return response()->json([
                'message' => 'Default template set successfully',
                'data' => $template
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error setting default template: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to set default template',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function removeDefault(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            $template = InvoiceTemplate::findOrFail($id);
            if (!$template->is_default) {
                return response()->json([
                    'message' => 'Template is not set as default'
                ], 400);
            }
            $template->is_default = false;
            $template->save();
            DB::commit();
            return response()->json([
                'message' => 'Default status removed successfully',
                'data' => $template
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error removing default template: ' . $e->getMessage());
            return response()->json([
                'error' => 'Failed to remove default template',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:50',
            'width' => 'required|numeric|min:50|max:500',
            'height' => 'required|numeric|min:50|max:500',
            'text_elements' => 'nullable|array',
            'text_elements.*' => 'string',
            'text_positions' => 'nullable|array',
            'text_positions.*.x' => 'required|numeric|min:0',
            'text_positions.*.y' => 'required|numeric|min:0',
            'text_styles' => 'nullable|array',
            'text_styles.*' => 'array',
            'text_sizes' => 'nullable|array',
            'text_sizes.*.width' => 'required|numeric|min:20|max:500',
            'text_sizes.*.height' => 'required|numeric|min:20|max:500',
            'image_elements' => 'nullable|array',
            'image_elements.*' => [
                'string',
                function ($attribute, $value, $fail) {
                    if (filter_var($value, FILTER_VALIDATE_URL)) {
                        return true;
                    }
                    if (preg_match('/^data:image\/(png|jpg|jpeg|gif);base64,/', $value)) {
                        $base64 = substr($value, strpos($value, ',') + 1);
                        if (base64_decode($base64, true) !== false) {
                            return true;
                        }
                    }
                    $fail('The ' . $attribute . ' must be a valid URL or base64 data URL.');
                },
            ],
            'image_positions' => 'nullable|array',
            'image_positions.*.x' => 'required|numeric|min:0',
            'image_positions.*.y' => 'required|numeric|min:0',
            'image_sizes' => 'nullable|array',
            'image_sizes.*.width' => 'required|numeric|min:20|max:500',
            'image_sizes.*.height' => 'required|numeric|min:20|max:500',
            'placeholder_elements' => 'nullable|array',
            'placeholder_elements.*' => 'string',
            'placeholder_positions' => 'nullable|array',
            'placeholder_positions.*.x' => 'required|numeric|min:0',
            'placeholder_positions.*.y' => 'required|numeric|min:0',
            'placeholder_sizes' => 'nullable|array',
            'placeholder_sizes.*.width' => 'required|numeric|min:20|max:500',
            'placeholder_sizes.*.height' => 'required|numeric|min:20|max:500',
            'item_list_columns' => 'nullable|array',
            'item_list_columns.*.id' => 'required|string',
            'item_list_columns.*.label' => 'required|string',
            'item_list_columns.*.visible' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            $template = InvoiceTemplate::create($request->all());
            return response()->json(['data' => $template], 201);
        } catch (\Exception $e) {
            Log::error('Error storing template: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to store template'], 500);
        }
    }

    public function show($id)
    {
        try {
            $template = InvoiceTemplate::findOrFail($id);
            return response()->json(['data' => $template], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching template: ' . $e->getMessage());
            return response()->json(['error' => 'Template not found'], 404);
        }
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:50',
            'width' => 'required|numeric|min:50|max:500',
            'height' => 'required|numeric|min:50|max:500',
            'text_elements' => 'nullable|array',
            'text_elements.*' => 'string',
            'text_positions' => 'nullable|array',
            'text_positions.*.x' => 'required|numeric|min:0',
            'text_positions.*.y' => 'required|numeric|min:0',
            'text_styles' => 'nullable|array',
            'text_styles.*' => 'array',
            'text_sizes' => 'nullable|array',
            'text_sizes.*.width' => 'required|numeric|min:20|max:500',
            'text_sizes.*.height' => 'required|numeric|min:20|max:500',
            'image_elements' => 'nullable|array',
            'image_elements.*' => [
                'string',
                function ($attribute, $value, $fail) {
                    if (filter_var($value, FILTER_VALIDATE_URL)) {
                        return true;
                    }
                    if (preg_match('/^data:image\/(png|jpg|jpeg|gif);base64,/', $value)) {
                        $base64 = substr($value, strpos($value, ',') + 1);
                        if (base64_decode($base64, true) !== false) {
                            return true;
                        }
                    }
                    $fail('The ' . $attribute . ' must be a valid URL or base64 data URL.');
                },
            ],
            'image_positions' => 'nullable|array',
            'image_positions.*.x' => 'required|numeric|min:0',
            'image_positions.*.y' => 'required|numeric|min:0',
            'image_sizes' => 'nullable|array',
            'image_sizes.*.width' => 'required|numeric|min:20|max:500',
            'image_sizes.*.height' => 'required|numeric|min:20|max:500',
            'placeholder_elements' => 'nullable|array',
            'placeholder_elements.*' => 'string',
            'placeholder_positions' => 'nullable|array',
            'placeholder_positions.*.x' => 'required|numeric|min:0',
            'placeholder_positions.*.y' => 'required|numeric|min:0',
            'placeholder_sizes' => 'nullable|array',
            'placeholder_sizes.*.width' => 'required|numeric|min:20|max:500',
            'placeholder_sizes.*.height' => 'required|numeric|min:20|max:500',
            'item_list_columns' => 'nullable|array',
            'item_list_columns.*.id' => 'required|string',
            'item_list_columns.*.label' => 'required|string',
            'item_list_columns.*.visible' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            $template = InvoiceTemplate::findOrFail($id);
            $template->update($request->all());
            return response()->json(['data' => $template], 200);
        } catch (\Exception $e) {
            Log::error('Error updating template: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to update template'], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $template = InvoiceTemplate::findOrFail($id);
            $template->delete();
            return response()->json(['message' => 'Template deleted'], 200);
        } catch (\Exception $e) {
            Log::error('Error deleting template: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete template'], 500);
        }
    }

    public function getDefault()
    {
        try {
            Log::info('getDefault request received');
            $template = InvoiceTemplate::where('is_default', 1)->firstOrFail();
            Log::info('Default template found', ['template' => $template->toArray()]);
            return response()->json(['data' => $template], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching default template: ' . $e->getMessage());
            return response()->json(['error' => 'Default template not found'], 404);
        }
    }
}
?>