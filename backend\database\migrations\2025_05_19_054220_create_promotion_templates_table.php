<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('promotion_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // Template name for easy identification
            $table->json('customer_details')->nullable(); // Selected customer IDs
            $table->json('excel_details')->nullable(); // Excel data
            $table->text('message'); // Promotion message
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('promotion_templates');
    }
};
