{"name": "phpdocumentor/reflection-docblock", "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.4 || ^8.0", "phpdocumentor/type-resolver": "^1.7", "webmozart/assert": "^1.9.1", "phpdocumentor/reflection-common": "^2.2", "ext-filter": "*", "phpstan/phpdoc-parser": "^1.7|^2.0", "doctrine/deprecations": "^1.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpunit/phpunit": "^9.5", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "psalm/phar": "^5.26"}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "autoload-dev": {"psr-4": {"phpDocumentor\\Reflection\\": ["tests/unit", "tests/integration"]}}, "config": {"platform": {"php": "7.4.0"}, "allow-plugins": {"phpstan/extension-installer": true}}, "extra": {"branch-alias": {"dev-master": "5.x-dev"}}}