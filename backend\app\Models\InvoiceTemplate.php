<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceTemplate extends Model
{
    use HasFactory;

    protected $table = 'invoice_templates';

    protected $fillable = [
        'user_id',
        'name',
        'type',
        'width',
        'height',
        'text_elements',
        'text_positions',
        'text_styles',
        'text_sizes',
        'image_elements',
        'image_positions',
        'image_sizes',
        'placeholder_elements',
        'placeholder_positions',
        'placeholder_sizes',
        'item_list_columns',
        'is_default',
    ];

    protected $casts = [
        'text_elements' => 'array',
        'text_positions' => 'array',
        'text_styles' => 'array',
        'text_sizes' => 'array',
        'image_elements' => 'array',
        'image_positions' => 'array',
        'image_sizes' => 'array',
        'placeholder_elements' => 'array',
        'placeholder_positions' => 'array',
        'placeholder_sizes' => 'array',
        'item_list_columns' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
}
