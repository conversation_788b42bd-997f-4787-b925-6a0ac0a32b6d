<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Event\TestRunner;

use PHPUnit\Event\Event;
use PHPUnit\Event\Telemetry;

/**
 * @immutable
 *
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
final readonly class ChildProcessFinished implements Event
{
    private Telemetry\Info $telemetryInfo;
    private string $stdout;
    private string $stderr;

    public function __construct(Telemetry\Info $telemetryInfo, string $stdout, string $stderr)
    {
        $this->telemetryInfo = $telemetryInfo;
        $this->stdout        = $stdout;
        $this->stderr        = $stderr;
    }

    public function telemetryInfo(): Telemetry\Info
    {
        return $this->telemetryInfo;
    }

    public function stdout(): string
    {
        return $this->stdout;
    }

    public function stderr(): string
    {
        return $this->stderr;
    }

    public function asString(): string
    {
        return 'Child Process Finished';
    }
}
