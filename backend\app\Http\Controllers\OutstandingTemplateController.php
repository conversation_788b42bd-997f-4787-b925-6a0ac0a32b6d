<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\OutstandingTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OutstandingTemplateController extends Controller
{
    /**
     * Get all templates.
     */
    public function index()
    {
        $templates = OutstandingTemplate::all();
        return response()->json(['data' => $templates], 200);
    }

    /**
     * Get a single template by ID.
     */
    public function show($id)
    {
        $template = OutstandingTemplate::find($id);

        if (!$template) {
            return response()->json(['message' => 'Template not found'], 404);
        }

        return response()->json(['data' => $template], 200);
    }

    /**
     * Create a new template.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_name' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $template = OutstandingTemplate::create([
            'template_name' => $request->template_name,
            'message' => $request->message,
        ]);

        return response()->json(['data' => $template, 'message' => 'Template created successfully'], 201);
    }

    /**
     * Update an existing template.
     */
    public function update(Request $request, $id)
    {
        $template = OutstandingTemplate::find($id);

        if (!$template) {
            return response()->json(['message' => 'Template not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'template_name' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $template->update([
            'template_name' => $request->template_name,
            'message' => $request->message,
        ]);

        return response()->json(['data' => $template, 'message' => 'Template updated successfully'], 200);
    }

    /**
     * Delete a template.
     */
    public function destroy($id)
    {
        $template = OutstandingTemplate::find($id);

        if (!$template) {
            return response()->json(['message' => 'Template not found'], 404);
        }

        // Prevent deletion of default templates (assuming IDs 1-4 are default)
        // if ($id <= 4) {
        //     return response()->json(['message' => 'Default templates cannot be deleted'], 403);
        // }

        $template->delete();

        return response()->json(['message' => 'Template deleted successfully'], 200);
    }
}
