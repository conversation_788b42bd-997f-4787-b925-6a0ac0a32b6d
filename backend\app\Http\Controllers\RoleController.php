<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Log;
use Artisan;

class RoleController extends Controller
{
    public function index(Request $request)
    {
        $search = $request->query('search', '');
        $page = $request->query('page', 1);
        $perPage = 10;

        $query = Role::with('permissions');
        if ($search) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        $roles = $query->paginate($perPage, ['*'], 'page', $page);

        $roles->getCollection()->transform(function ($role) {
            return [
                'id' => $role->id,
                'name' => $role->name,
                'description' => $role->description,
                'permissions' => $role->permissions->pluck('name'),
            ];
        });

        return response()->json([
            'data' => $roles->items(),
            'current_page' => $roles->currentPage(),
            'last_page' => $roles->lastPage(),
            'total' => $roles->total(),
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|unique:roles,name',
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
            'permissions.*' => 'string|exists:permissions,name'
        ]);

        $role = Role::create([
            'name' => $request->name,
            'description' => $request->description,
            'guard_name' => 'api'
        ]);

        if ($request->has('permissions')) {
            Log::info('Storing permissions for role ' . $role->name . ': ' . json_encode($request->permissions));
            $role->syncPermissions($request->permissions);
            Artisan::call('permission:cache-reset');
            Log::info('Permission cache cleared after storing permissions for role: ' . $role->name);
        }

        return response()->json([
            'id' => $role->id,
            'name' => $role->name,
            'description' => $role->description,
            'permissions' => $role->permissions->pluck('name'),
        ], 201);
    }

    public function show(Role $role)
    {
        return response()->json([
            'id' => $role->id,
            'name' => $role->name,
            'description' => $role->description,
            'permissions' => $role->permissions->pluck('name'),
        ]);
    }

    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => 'required|string|unique:roles,name,' . $role->id,
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
            'permissions.*' => 'string|exists:permissions,name'
        ]);

        $role->update([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        if ($request->has('permissions')) {
            Log::info('Updating permissions for role ' . $role->name . ': ' . json_encode($request->permissions));
            $role->syncPermissions($request->permissions);
        } else {
            $role->syncPermissions([]); // Clear permissions if none provided
        }

        Artisan::call('permission:cache-reset');
        Log::info('Permission cache cleared after updating permissions for role: ' . $role->name);

        return response()->json([
            'id' => $role->id,
            'name' => $role->name,
            'description' => $role->description,
            'permissions' => $role->permissions->pluck('name'),
        ]);
    }

    public function destroy(Role $role)
    {
        if (in_array(strtolower($role->name), ['admin', 'superadmin'])) {
            return response()->json(['message' => 'Cannot delete admin or superadmin roles'], 403);
        }

        $name = $role->name;
        $role->delete();
        Artisan::call('permission:cache-reset');
        Log::info('Deleted role: ' . $name);

        return response()->noContent();
    }

    public function assignPermissions(Request $request, Role $role)
    {
        $request->validate([
            'permissions' => 'required|array',
            'permissions.*' => 'string|exists:permissions,name'
        ]);

        Log::info('Assigning permissions for role ' . $role->name . ': ' . json_encode($request->permissions));
        $role->syncPermissions($request->permissions);
        Artisan::call('permission:cache-reset');
        Log::info('Permission cache cleared after assigning permissions for role: ' . $role->name);

        return response()->json([
            'id' => $role->id,
            'name' => $role->name,
            'description' => $role->description,
            'permissions' => $role->permissions->pluck('name'),
        ]);
    }
}