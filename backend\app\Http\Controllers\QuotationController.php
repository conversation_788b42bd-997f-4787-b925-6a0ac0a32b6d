<?php

namespace App\Http\Controllers;

use App\Models\Quotation;
use App\Models\QuotationItem;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class QuotationController extends Controller
{
    public function index()
{
    try {
        $quotations = Quotation::with(['customer', 'items'])->get();
        return response()->json($quotations, 200);
    } catch (\Exception $e) {
        Log::error('Error fetching quotations: ' . $e->getMessage());
        return response()->json(['error' => 'Failed to fetch quotations'], 500);
    }
}

    public function store(Request $request)
    {
        Log::info('Quotation store request:', $request->all());

        try {
            $validated = $request->validate([
                'customer_id' => 'required|exists:customers,id',
                'date' => 'required|date',
                'time' => 'required',
                'agent_name' => 'nullable|string|max:255',
                'agent_phone' => 'nullable|string|max:20',
                'total' => 'required|numeric|min:0',
                'prepared_by' => 'required|string|max:255',
                'approved_by' => 'nullable|string|max:255',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,product_id',
                'items.*.description' => 'required|string|max:255',
                'items.*.qty' => 'required|integer|min:1',
                'items.*.mrp' => 'required|numeric|min:0',
                'items.*.selling_price' => 'required|numeric|min:0',
                'items.*.free_qty' => 'nullable|integer|min:0',
                'items.*.discount_amount' => 'nullable|numeric|min:0',
                'items.*.special_discount_amount' => 'nullable|numeric|min:0',
                'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
                'items.*.discount_scheme_type' => 'nullable|string|in:product,category',
                'items.*.total' => 'required|numeric|min:0',
            ]);

            return DB::transaction(function () use ($validated, $request) {
                // Generate sequential quotation number (QT-001, QT-002, etc.)
                $lastQuotation = Quotation::orderBy('id', 'desc')->first();
                $nextNumber = $lastQuotation ? $lastQuotation->id + 1 : 1;
                $quotationNo = 'QT-' . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);

                $quotation = Quotation::create([
                    'quotation_no' => $quotationNo,
                    'customer_id' => $validated['customer_id'],
                    'date' => $validated['date'],
                    'time' => $validated['time'],
                    'agent_name' => $validated['agent_name'],
                    'agent_phone' => $validated['agent_phone'],
                    'total' => $validated['total'],
                'prepared_by' => $validated['prepared_by'],
                'approved_by' => $validated['approved_by'] ?? null,
            ]);

                foreach ($validated['items'] as $item) {
                    QuotationItem::create([
                        'quotation_id' => $quotation->id,
                        'product_id' => $item['product_id'],
                        'description' => $item['description'],
                        'qty' => $item['qty'],
                        'mrp' => $item['mrp'],
                        'selling_price' => $item['selling_price'],
                        'free_qty' => $item['free_qty'] ?? 0,
                        'discount_amount' => $item['discount_amount'] ?? 0,
                        'special_discount_amount' => $item['special_discount_amount'] ?? 0,
                        'discount_percentage' => $item['discount_percentage'] ?? 0,
                        'discount_scheme_type' => $item['discount_scheme_type'],
                        'total' => $item['total'],
                    ]);
                }

                return response()->json($quotation->load(['customer', 'items']), 201);
            });
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation error saving quotation: ' . json_encode($e->errors()));
            return response()->json(['error' => $e->errors()], 422);
        } catch (\Exception $e) {
            Log::error('Error saving quotation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json(['error' => 'Failed to save quotation: ' . $e->getMessage()], 500);
        }
    }

    public function show($id)
    {
        try {
            $quotation = Quotation::with(['customer', 'items'])->findOrFail($id);
            return response()->json($quotation);
        } catch (\Exception $e) {
            Log::error('Error fetching quotation: ' . $e->getMessage());
            return response()->json(['error' => 'Quotation not found'], 404);
        }
    }

    public function update(Request $request, $id)
    {
        Log::info('Quotation update request:', $request->all());

        try {
            $validated = $request->validate([
                'customer_id' => 'required|exists:customers,id',
                'date' => 'required|date',
                'time' => 'required',
                'agent_name' => 'nullable|string|max:255',
                'agent_phone' => 'nullable|string|max:20',
                'total' => 'required|numeric|min:0',
                'prepared_by' => 'required|string|max:255',
                'received_by' => 'nullable|string|max:255',
                'checked_by' => 'nullable|string|max:255',
                'authorized_by' => 'nullable|string|max:255',
                'approved' => 'nullable|string|max:255',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,product_id',
                'items.*.description' => 'required|string|max:255',
                'items.*.qty' => 'required|integer|min:1',
                'items.*.mrp' => 'required|numeric|min:0',
                'items.*.selling_price' => 'required|numeric|min:0',
                'items.*.free_qty' => 'nullable|integer|min:0',
                'items.*.discount_amount' => 'nullable|numeric|min:0',
                'items.*.special_discount_amount' => 'nullable|numeric|min:0',
                'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
                'items.*.discount_scheme_type' => 'nullable|string|in:product,category',
                'items.*.total' => 'required|numeric|min:0',
            ]);

            return DB::transaction(function () use ($validated, $id) {
                $quotation = Quotation::findOrFail($id);
                $quotation->update([
                    'customer_id' => $validated['customer_id'],
                    'date' => $validated['date'],
                    'time' => $validated['time'],
                    'agent_name' => $validated['agent_name'],
                    'agent_phone' => $validated['agent_phone'],
                    'total' => $validated['total'],
                'prepared_by' => $validated['prepared_by'],
                'received_by' => $validated['received_by'] ?? null,
                'checked_by' => $validated['checked_by'] ?? null,
                'authorized_by' => $validated['authorized_by'] ?? null,
                'approved_by' => $validated['approved_by'] ?? null,
            ]);

                $quotation->items()->delete();
                foreach ($validated['items'] as $item) {
                    QuotationItem::create([
                        'quotation_id' => $quotation->id,
                        'product_id' => $item['product_id'],
                        'description' => $item['description'],
                        'qty' => $item['qty'],
                        'mrp' => $item['mrp'],
                        'selling_price' => $item['selling_price'],
                        'free_qty' => $item['free_qty'] ?? 0,
                        'discount_amount' => $item['discount_amount'] ?? 0,
                        'special_discount_amount' => $item['special_discount_amount'] ?? 0,
                        'discount_percentage' => $item['discount_percentage'] ?? 0,
                        'discount_scheme_type' => $item['discount_scheme_type'],
                        'total' => $item['total'],
                    ]);
                }

                return response()->json($quotation->load(['customer', 'items']));
            });
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation error updating quotation: ' . json_encode($e->errors()));
            return response()->json(['error' => $e->errors()], 422);
        } catch (\Exception $e) {
            Log::error('Error updating quotation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json(['error' => 'Failed to update quotation: ' . $e->getMessage()], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $quotation = Quotation::findOrFail($id);
            $quotation->items()->delete();
            $quotation->delete();
            return response()->json(['message' => 'Quotation deleted successfully']);
        } catch (\Exception $e) {
            Log::error('Error deleting quotation: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete quotation'], 500);
        }
    }
    public function getUsers()
{
    try {
        $users = User::select('id', 'name')->get();
        return response()->json($users);
    } catch (\Exception $e) {
        Log::error('Error fetching users: ' . $e->getMessage());
        return response()->json(['error' => 'Failed to fetch users'], 500);
    }
}

    public function approve($id, Request $request)
    {
        try {
            $quotation = Quotation::findOrFail($id);
            // Example approval logic: set approved_by to current user or request data
            $approvedBy = $request->input('approved_by', 'System'); // fallback to 'System' if not provided
            $quotation->approved_by = $approvedBy;
            $quotation->save();

            return response()->json([
                'message' => 'Quotation approved successfully',
                'quotation' => $quotation->load(['customer', 'items'])
            ]);
        } catch (\Exception $e) {
            Log::error('Error approving quotation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'quotation_id' => $id,
                'request' => $request->all(),
            ]);
            return response()->json(['error' => 'Failed to approve quotation: ' . $e->getMessage()], 500);
        }
    }

    public function reject($id, Request $request)
    {
        try {
            $quotation = Quotation::findOrFail($id);
            $rejectedBy = $request->input('rejected_by', 'System'); // fallback to 'System' if not provided
            $quotation->rejected_by = $rejectedBy;
            $quotation->save();

            return response()->json([
                'message' => 'Quotation rejected successfully',
                'quotation' => $quotation->load(['customer', 'items'])
            ]);
        } catch (\Exception $e) {
            Log::error('Error rejecting quotation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'quotation_id' => $id,
                'request' => $request->all(),
            ]);
            return response()->json(['error' => 'Failed to reject quotation: ' . $e->getMessage()], 500);
        }
    }
}