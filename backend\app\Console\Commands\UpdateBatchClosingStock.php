<?php

namespace App\Console\Commands;

use App\Http\Controllers\StockReportController;
use Illuminate\Console\Command;

class UpdateBatchClosingStock extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:update-batch-closing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update closing stock quantities for all product variants (batches)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting batch closing stock calculation...');
        
        $controller = new StockReportController();
        $response = $controller->updateBatchClosingStock();
        
        $data = json_decode($response->getContent(), true);
        
        if ($response->getStatusCode() === 200) {
            $this->info("✅ " . $data['message']);
            $this->info("Updated {$data['updated_count']} variant(s)");
        } else {
            $this->error("❌ " . ($data['error'] ?? 'Unknown error'));
            return Command::FAILURE;
        }
        
        return Command::SUCCESS;
    }
}
