<?php return ['<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON>i ', '<PERSON><PERSON> ', '<PERSON>u ', '<PERSON> ', '<PERSON> ', '<PERSON>ang ', '<PERSON><PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>o ', '<PERSON>an ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON> ', '<PERSON>o ', '<PERSON>g ', '<PERSON>an ', '<PERSON> ', '<PERSON>an ', '<PERSON> ', '<PERSON>ua ', '<PERSON> ', 'Sheng ', 'Jian ', 'Ge ', 'Zha ', 'Kai ', 'Chuang ', 'Juan ', 'Chan ', 'Tuan ', 'Lu ', 'Li ', 'Fou ', 'Shan ', 'Piao ', 'Kou ', 'Jiao ', 'Gua ', 'Qiao ', 'Jue ', 'Hua ', 'Zha ', 'Zhuo ', 'Lian ', 'Ju ', 'Pi ', 'Liu ', 'Gui ', 'Jiao ', 'Gui ', 'Jian ', 'Jian ', 'Tang ', 'Huo ', 'Ji ', 'Jian ', 'Yi ', 'Jian ', 'Zhi ', 'Chan ', 'Cuan ', 'Mo ', 'Li ', 'Zhu ', 'Li ', 'Ya ', 'Quan ', 'Ban ', 'Gong ', 'Jia ', 'Wu ', 'Mai ', 'Lie ', 'Jin ', 'Keng ', 'Xie ', 'Zhi ', 'Dong ', 'Zhu ', 'Nu ', 'Jie ', 'Qu ', 'Shao ', 'Yi ', 'Zhu ', 'Miao ', 'Li ', 'Jing ', 'Lao ', 'Lao ', 'Juan ', 'Kou ', 'Yang ', 'Wa ', 'Xiao ', 'Mou ', 'Kuang ', 'Jie ', 'Lie ', 'He ', 'Shi ', 'Ke ', 'Jing ', 'Hao ', 'Bo ', 'Min ', 'Chi ', 'Lang ', 'Yong ', 'Yong ', 'Mian ', 'Ke ', 'Xun ', 'Juan ', 'Qing ', 'Lu ', 'Pou ', 'Meng ', 'Lai ', 'Le ', 'Kai ', 'Mian ', 'Dong ', 'Xu ', 'Xu ', 'Kan ', 'Wu ', 'Yi ', 'Xun ', 'Weng ', 'Sheng ', 'Lao ', 'Mu ', 'Lu ', 'Piao ', 'Shi ', 'Ji ', 'Qin ', 'Qiang ', 'Jiao ', 'Quan ', 'Yang ', 'Yi ', 'Jue ', 'Fan ', 'Juan ', 'Tong ', 'Ju ', 'Dan ', 'Xie ', 'Mai ', 'Xun ', 'Xun ', 'Lu ', 'Li ', 'Che ', 'Rang ', 'Quan ', 'Bao ', 'Shao ', 'Yun ', 'Jiu ', 'Bao ', 'Gou ', 'Wu '];
