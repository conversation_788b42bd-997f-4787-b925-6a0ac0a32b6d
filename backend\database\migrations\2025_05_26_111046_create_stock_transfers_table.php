<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_transfers', function (Blueprint $table) {
            $table->id();
            $table->string('transfer_number')->unique();
            $table->string('branch_id'); 
            $table->foreign('branch_id')
                  ->references('branch_id')
                  ->on('branches')
                  ->onDelete('cascade'); 
            $table->date('transfer_date');
            $table->decimal('total_quantity', 10, 2)->default(0);
            $table->decimal('total_value', 10, 2)->default(0);
            $table->string('status')->default('Pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_transfers');
    }
};
