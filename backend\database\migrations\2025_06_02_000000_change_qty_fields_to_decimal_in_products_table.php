<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->decimal('minimum_stock_quantity', 10, 2)->nullable()->change();
            $table->decimal('opening_stock_quantity', 10, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->integer('minimum_stock_quantity')->nullable()->change();
            $table->integer('opening_stock_quantity')->nullable()->change();
        });
    }
};
