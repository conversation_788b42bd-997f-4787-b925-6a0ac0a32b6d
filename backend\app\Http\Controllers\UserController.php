<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    public function index()
    {
        $users = User::with('roles')->paginate();
        return response()->json([
            'data' => $users->items(),
            'meta' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
            ]
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'password' => ['required', Rules\Password::defaults()],
            'role' => 'required|string|exists:roles,name',
        ]);

        $user = User::create([
            'name' => $request->name,
            'username' => $request->username,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'status' => 'active',
            'role' => $request->role, // Add this line to fix the error
        ]);

        $user->assignRole($request->role);
        
        // Log user activity
        \App\Http\Controllers\UserActivityController::logActivity(
            auth()->id(),
            'User Created',
            'Users',
            $user->id,
            [
                'user_name' => $user->name,
                'email' => $user->email,
                'role' => $user->role
            ]
        );

        return response()->json($user->load('roles'), 201);
    }

    public function show(User $user)
    {
        return $user->load('roles', 'permissions');
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'username' => 'sometimes|string|max:255|unique:users,username,'.$user->id,
            'email' => 'sometimes|string|email|max:255|unique:users,email,'.$user->id,
            'phone' => 'nullable|string|max:20',
            'password' => ['sometimes', Rules\Password::defaults()],
            'role' => 'sometimes|string|exists:roles,name',
            'status' => 'sometimes|in:active,inactive',
        ]);

        $user->update($request->only([
            'name', 'username', 'email', 'phone', 'status'
        ]));

        if ($request->has('password')) {
            $user->update(['password' => Hash::make($request->password)]);
        }

        if ($request->has('role')) {
            $user->syncRoles($request->role);
        }

        return response()->json($user->load('roles', 'permissions'));
    }

    public function destroy(User $user)
    {
        // Log the delete activity
        \App\Http\Controllers\UserActivityController::logActivity(
            auth()->id() ?? request()->user()->id ?? 1,
            'User Deleted',
            'Users',
            $user->id,
            [
                'user_name' => $user->name,
                'email' => $user->email,
                'deleted_by' => auth()->id() ?? request()->user()->id ?? 1
            ]
        );
        
        $user->delete();
        return response()->noContent();
    }

    public function getDeletedUsers()
    {
        $users = User::onlyTrashed()->with('roles')->paginate();
        return response()->json([
            'data' => $users->items(),
            'meta' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
            ]
        ]);
    }

    public function restoreUser($id)
    {
        $user = User::onlyTrashed()->findOrFail($id);
        $user->restore();
        
        // Log the restore activity
        \App\Http\Controllers\UserActivityController::logActivity(
            auth()->id() ?? request()->user()->id ?? 1,
            'User Restored',
            'Users',
            $user->id,
            [
                'user_name' => $user->name,
                'email' => $user->email,
                'restored_by' => auth()->id() ?? request()->user()->id ?? 1
            ]
        );
        
        return response()->json($user->load('roles'));
    }

    public function forceDeleteUser($id)
    {
        $user = User::onlyTrashed()->findOrFail($id);
        
        // Log the force delete activity
        \App\Http\Controllers\UserActivityController::logActivity(
            auth()->id() ?? request()->user()->id ?? 1,
            'User Permanently Deleted',
            'Users',
            $user->id,
            [
                'user_name' => $user->name,
                'email' => $user->email,
                'permanently_deleted_by' => auth()->id() ?? request()->user()->id ?? 1
            ]
        );
        
        $user->forceDelete();
        return response()->noContent();
    }

    public function assignPermissions(Request $request, User $user)
    {
        $request->validate([
            'permissions' => 'required|array',
            'permissions.*' => 'string|exists:permissions,name'
        ]);

        $user->syncPermissions($request->permissions);
        return response()->json($user->load('permissions'));
    }

    public function activateUser(User $user)
    {
        $user->update(['status' => 'active']);
        return response()->json([
            'message' => 'User activated successfully',
            'user' => $user->load('roles')
        ]);
    }

    public function updateStatus(Request $request, User $user)
    {
        $request->validate([
            'is_active' => 'required|boolean'
        ]);

        $user->update(['is_active' => $request->is_active]);
        return response()->json([
            'message' => 'User status updated',
            'user' => $user->load('roles')
        ]);
    }
    
    /**
     * Get the branch assignment for a specific user
     */
    public function getUserBranch($userId)
    {
        try {
            \Log::debug("Getting branch assignment for user ID: {$userId}");
            $user = User::findOrFail($userId);
            \Log::debug("Found user: {$user->name} ({$user->email})");
            
            // Get branch user assignment
            $branchUserQuery = \App\Models\BranchUser::where('user_id', $userId)
                ->where('is_active', true);
                
            \Log::debug("SQL query: " . $branchUserQuery->toSql());
            \Log::debug("Query bindings: " . json_encode($branchUserQuery->getBindings()));
            
            $branchUser = $branchUserQuery->first();
            
            if (!$branchUser) {
                \Log::info("User {$userId} is not assigned to any branch");
                return response()->json([
                    'success' => true,
                    'message' => 'User is not assigned to any branch',
                    'data' => null
                ], 200);
            }
            
            \Log::debug("User {$userId} is assigned to branch ID: {$branchUser->branch_id}");
            $branch = \App\Models\Branch::find($branchUser->branch_id);
            
            $responseData = [
                'success' => true,
                'data' => [
                    'branch_id' => $branchUser->branch_id,
                    'branch_name' => $branch ? $branch->branch_name : 'Unknown Branch',
                    'is_active' => $branchUser->is_active
                ]
            ];
            
            \Log::debug("Returning branch data: " . json_encode($responseData['data']));
            return response()->json($responseData, 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching user branch',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
