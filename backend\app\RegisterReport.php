<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class RegisterReport extends Model
{
    protected $table = 'register_reports';

    protected $fillable = [
        'register_id',
        'user_id',
        'terminal_id',
        'status',
        'opened_at',
        'closed_at',
        'opening_balance',
        'closing_balance',
        'actual_cash',
        'total_sales',
        'total_sales_qty',
    ];

    public function user()
    {
        return $this->belongsTo(\App\Models\User::class, 'user_id');
    }

    public function register()
    {
        return $this->belongsTo(\App\Models\CashRegistry::class, 'register_id');
    }
}
