<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * Recommendation: Use $guarded = [] for simplicity during development
     * if you trust your input source (like a validated Form Request),
     * or list all fields explicitly in $fillable for production.
     */
    // protected $fillable = [
    //     'invoice_no',
    //     'invoice_date',
    //     'invoice_time',
    //     'customer_name',
    //     'customer_address',
    //     'customer_phone',
    //     'customer_email',
    //     'payment_method',
    //     'purchase_amount',
    //     'subtotal',
    //     'tax_amount',
    //     'total_amount',
    //     'balance',
    //     'status',
    // ];
    protected $fillable = [
    'invoice_no', 'invoice_date', 'invoice_time', 'customer_id', 'customer_name',
    'customer_address', 'customer_phone', 'customer_email', 'payment_method',
    'purchase_amount', 'subtotal', 'tax_percentage', 'tax_amount', 'total_amount', 'balance', 'status',
    'quotation_id', // Add this line
    'prepared_by',
    'approved_by',
    'rejected_by',
    'cheque_no', 'bank_name', 'issue_date',
    'deleted_by', // <-- Added this line

];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'invoice_date' => 'date',
        'invoice_time' => 'datetime:H:i', // Adjust format if needed
        'purchase_amount' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'balance' => 'decimal:2',
        'issue_date' => 'date',
    ];

    /**
     * Get the items for the invoice.
     */
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function quotation()
    {
        return $this->belongsTo(Quotation::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function deletedByUser()
    {
        return $this->belongsTo(User::class, 'deleted_by')->withTrashed();
    }
    
    public function approvedByUser()
    {
        return $this->belongsTo(User::class, 'approved_by')->withTrashed();
    }

    // public static function generateInvoiceNumber(): string
    // {
    //     $year = now()->format('Y'); // Current year, e.g., 2025
    //     $prefix = "INV-{$year}-";

    //     // Find the last invoice number for the current year
    //     $lastInvoice = DB::table('invoices')
    //         ->where('invoice_no', 'like', $prefix . '%')
    //         ->orderBy('invoice_no', 'desc')
    //         ->first();

    //     $nextNumber = 1;
    //     if ($lastInvoice) {
    //         $lastNumber = (int) substr($lastInvoice->invoice_no, strlen($prefix));
    //         $nextNumber = $lastNumber + 1;
    //     }

    //     // Format with leading zeros (e.g., 0001)
    //     return $prefix . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    // }

    public static function generateInvoiceNumber(): string
{
    $prefix = "INV-";

    // Find the last invoice number with prefix "INV-"
    $lastInvoice = DB::table('invoices')
        ->where('invoice_no', 'like', $prefix . '%')
        ->orderBy('invoice_no', 'desc')
        ->first();

    $nextNumber = 1;
    if ($lastInvoice) {
        // Extract numeric part after prefix
        $lastNumber = (int) substr($lastInvoice->invoice_no, strlen($prefix));
        $nextNumber = $lastNumber + 1;
    }

    // Format with leading zeros (e.g., 001)
    return $prefix . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
}


    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invoice) {
            if (empty($invoice->invoice_no)) {
                $invoice->invoice_no = static::generateInvoiceNumber();
            }
        });
    }
}