<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveBatchFieldsFromProductsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'batch_number',
                'expiry_date',
                'buying_cost',
                'sales_price',
                'minimum_price',
                'wholesale_price',
                'barcode',
                'mrp',
                'minimum_stock_quantity',
                'opening_stock_quantity',
                'opening_stock_value',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('batch_number')->nullable();
            $table->date('expiry_date')->nullable();
            $table->decimal('buying_cost', 10, 2);
            $table->decimal('sales_price', 10, 2);
            $table->decimal('minimum_price', 10, 2)->nullable();
            $table->decimal('wholesale_price', 10, 2)->nullable();
            $table->string('barcode')->unique()->nullable();
            $table->decimal('mrp', 10, 2);
            $table->integer('minimum_stock_quantity')->nullable();
            $table->integer('opening_stock_quantity')->nullable();
            $table->decimal('opening_stock_value', 10, 2)->nullable();
        });
    }
}
