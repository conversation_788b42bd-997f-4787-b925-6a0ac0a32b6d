<?php declare(strict_types=1);
/*
 * This file is part of phpunit/php-code-coverage.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON><PERSON>\CodeCoverage;

use RuntimeException;

final class NoCodeCoverageDriverAvailableException extends RuntimeException implements Exception
{
    public function __construct()
    {
        parent::__construct('No code coverage driver available');
    }
}
