<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserLog extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'action',
        'ip_address',
        'user_agent',
        'details',
        'module',
        'record_id',
        'old_values',
        'new_values'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'details' => 'array'
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByAction($query, $action)
    {
        return $query->where('action', 'like', "%{$action}%");
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    public function scopeByModule($query, $module)
    {
        return $query->where('module', $module);
    }

    // Helper methods
    public function getFormattedActionAttribute()
    {
        $action = strtolower($this->action);
        
        if (str_contains($action, 'login')) {
            return 'User Login';
        } elseif (str_contains($action, 'logout')) {
            return 'User Logout';
        } elseif (str_contains($action, 'create')) {
            return 'Record Created';
        } elseif (str_contains($action, 'update')) {
            return 'Record Updated';
        } elseif (str_contains($action, 'delete')) {
            return 'Record Deleted';
        } elseif (str_contains($action, 'restore')) {
            return 'Record Restored';
        } elseif (str_contains($action, 'export')) {
            return 'Data Exported';
        } elseif (str_contains($action, 'import')) {
            return 'Data Imported';
        }
        
        return ucfirst($this->action);
    }

    public function getActionColorAttribute()
    {
        $action = strtolower($this->action);
        
        if (str_contains($action, 'login')) {
            return 'green';
        } elseif (str_contains($action, 'logout')) {
            return 'red';
        } elseif (str_contains($action, 'create')) {
            return 'blue';
        } elseif (str_contains($action, 'update')) {
            return 'yellow';
        } elseif (str_contains($action, 'delete')) {
            return 'red';
        } elseif (str_contains($action, 'restore')) {
            return 'green';
        }
        
        return 'gray';
    }
} 