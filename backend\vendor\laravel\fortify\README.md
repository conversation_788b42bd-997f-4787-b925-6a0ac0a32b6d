<p align="center"><img width="358" height="66" src="/art/logo.svg" alt="Logo Laravel Fortify"></p>

<p align="center">
<a href="https://github.com/laravel/fortify/actions"><img src="https://github.com/laravel/fortify/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/fortify"><img src="https://img.shields.io/packagist/dt/laravel/fortify" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/fortify"><img src="https://img.shields.io/packagist/v/laravel/fortify" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/fortify"><img src="https://img.shields.io/packagist/l/laravel/fortify" alt="License"></a>
</p>

## Introduction

Laravel Fortify is a frontend agnostic authentication backend for Laravel. Fortify powers the registration, authentication, and two-factor authentication features of [Laravel Jetstream](https://github.com/laravel/jetstream).

## Official Documentation

Documentation for Fortify can be found on the [Laravel website](https://laravel.com/docs/fortify).

## Contributing

Thank you for considering contributing to Fortify! You can read the contribution guide [here](.github/CONTRIBUTING.md).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

Please review [our security policy](https://github.com/laravel/fortify/security/policy) on how to report security vulnerabilities.

## License

Laravel Fortify is open-sourced software licensed under the [MIT license](LICENSE.md).
