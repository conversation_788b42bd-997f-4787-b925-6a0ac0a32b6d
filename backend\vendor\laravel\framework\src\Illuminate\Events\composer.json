{"name": "illuminate/events", "description": "The Illuminate Events package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "illuminate/bus": "^11.0", "illuminate/collections": "^11.0", "illuminate/container": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0"}, "autoload": {"psr-4": {"Illuminate\\Events\\": ""}, "files": ["functions.php"]}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}