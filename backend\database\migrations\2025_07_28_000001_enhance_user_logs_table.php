<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('user_logs', function (Blueprint $table) {
            $table->text('user_agent')->nullable()->after('ip_address');
            $table->json('details')->nullable()->after('user_agent');
            $table->string('module')->nullable()->after('details');
            $table->unsignedBigInteger('record_id')->nullable()->after('module');
            $table->json('old_values')->nullable()->after('record_id');
            $table->json('new_values')->nullable()->after('old_values');
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::table('user_logs', function (Blueprint $table) {
            $table->dropColumn([
                'user_agent',
                'details',
                'module',
                'record_id',
                'old_values',
                'new_values',
                'deleted_at'
            ]);
        });
    }
}; 