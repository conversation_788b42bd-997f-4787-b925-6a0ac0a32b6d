<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Unknown default region, use the first alphabetically.
 */
return [
    'formats' => [
        'LT' => 'HH.mm',
        'LTS' => 'HH.mm:ss',
        'L' => 'DD-MM-YY',
        'LL' => 'MMMM [di] DD, YYYY',
        'LLL' => 'DD MMM HH.mm',
        'LLLL' => 'MMMM DD, YYYY HH.mm',
    ],
    'months' => ['yanüari', 'febrüari', 'mart', 'aprel', 'mei', 'yüni', 'yüli', 'ougùstùs', 'sèptèmber', 'oktober', 'novèmber', 'desèmber'],
    'months_short' => ['yan', 'feb', 'mar', 'apr', 'mei', 'yün', 'yül', 'oug', 'sèp', 'okt', 'nov', 'des'],
    'weekdays' => ['djadomingo', 'djaluna', 'djamars', 'djawebs', 'djarason', 'djabierne', 'djasabra'],
    'weekdays_short' => ['do', 'lu', 'ma', 'we', 'ra', 'bi', 'sa'],
    'weekdays_min' => ['do', 'lu', 'ma', 'we', 'ra', 'bi', 'sa'],
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 1,
    'year' => ':count aña',
    'month' => ':count luna',
    'week' => ':count siman',
    'day' => ':count dia',
    'hour' => ':count ora',
    'minute' => ':count minüt',
    'second' => ':count sekònde',
    'list' => [', ', ' i '],
];
