<?php

namespace <PERSON><PERSON>\Fortify\Http\Responses;

use Illuminate\Http\JsonResponse;
use <PERSON><PERSON>\Fortify\Contracts\VerifyEmailResponse as VerifyEmailResponseContract;
use <PERSON>vel\Fortify\Fortify;

class VerifyEmailResponse implements VerifyEmailResponseContract
{
    /**
     * Create an HTTP response that represents the object.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function toResponse($request)
    {
        return $request->wantsJson()
            ? new JsonResponse('', 204)
            : redirect()->intended(Fortify::redirects('email-verification').'?verified=1');
    }
}
