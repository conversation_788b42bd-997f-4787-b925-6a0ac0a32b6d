{"env": {"browser": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "react/no-unescaped-entities": "off"}, "settings": {"react": {"version": "detect"}}}