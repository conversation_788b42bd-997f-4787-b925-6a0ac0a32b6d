<?php

// app/Http/Middleware/CheckOpenRegister.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckOpenRegister
{
    public function handle(Request $request, Closure $next): Response
    {
        $userId = $request->user()->id;

        $openRegister = \App\Models\CashRegistry::where('user_id', $userId)
            ->where('status', 'open')
            ->whereNull('closed_at')
            ->exists();

        if (!$openRegister) {
            return response()->json([
                'message' => 'You must open a register before processing sales'
            ], 403);
        }

        return $next($request);
    }
}