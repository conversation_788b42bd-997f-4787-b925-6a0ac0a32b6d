<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Encoding\CannotDecodeContent;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key;
use SplFileObject;
use Throwable;

use function assert;
use function base64_decode;
use function is_string;

final class InMemory implements Key
{
    private string $contents;
    private string $passphrase;

    private function __construct(string $contents, string $passphrase)
    {
        $this->contents   = $contents;
        $this->passphrase = $passphrase;
    }

    public static function empty(): self
    {
        return new self('', '');
    }

    public static function plainText(string $contents, string $passphrase = ''): self
    {
        return new self($contents, $passphrase);
    }

    public static function base64Encoded(string $contents, string $passphrase = ''): self
    {
        $decoded = base64_decode($contents, true);

        if ($decoded === false) {
            throw CannotDecodeContent::invalidBase64String();
        }

        return new self($decoded, $passphrase);
    }

    /** @throws FileCouldNotBeRead */
    public static function file(string $path, string $passphrase = ''): self
    {
        try {
            $file = new SplFileObject($path);
        } catch (Throwable $exception) {
            throw FileCouldNotBeRead::onPath($path, $exception);
        }

        $contents = $file->fread($file->getSize());
        assert(is_string($contents));

        return new self($contents, $passphrase);
    }

    public function contents(): string
    {
        return $this->contents;
    }

    public function passphrase(): string
    {
        return $this->passphrase;
    }
}
