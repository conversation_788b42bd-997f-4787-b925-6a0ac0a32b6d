<?php

/**
 * This file is part of the ramsey/uuid library
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @copyright Copyright (c) <PERSON> <<EMAIL>>
 * @license http://opensource.org/licenses/MIT MIT
 */

declare(strict_types=1);

namespace <PERSON>\Uuid\Rfc4122;

use <PERSON>\Uuid\Uuid;

/**
 * The max UUID is special form of UUID that is specified to have all 128 bits
 * set to one
 *
 * @psalm-immutable
 */
final class MaxUuid extends Uuid implements UuidInterface
{
}
