<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class PermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        Log::info('Cleared Spatie permission cache');

        // Check if required tables exist
        $requiredTables = ['permissions', 'roles', 'model_has_roles', 'role_has_permissions', 'model_has_permissions'];
        foreach ($requiredTables as $table) {
            if (!Schema::hasTable($table)) {
                Log::error("Table `$table` does not exist. Please run Spatie Permission migrations.");
                throw new \Exception("Table `$table` is missing. Run `php artisan migrate` to create Spatie Permission tables.");
            }
        }

        $superAdminOnlyPaths = [
            'UserManagement',
            'UserList',
            // 'UserModal',
            'RoleList',
            'RecycleBin',
        ];

        // Define page paths (permissions)
        $pagePaths = [
            'dashboard',
            'items',
            'expiry',
            'store-locations',
            'itemageanalyze',
            'StockReport',
            'UserActivityReport',
            'ItemWiseStockReport',
            'StockRecheck',
            'BarcodePage',
            // 'StockTransfer', // Not used
            'categories',
            'companies',
            // 'CategoryForm',
            'units',
            // 'UnitForm',
            'purchasing',
            'PurchaseReturn',
            // 'PurchaseInvoice',
            'PurchaseOrder',
            'suppliers',
            // 'SupplierForm', // Used as child in Suppliers, keep
            'sales',
            'SalesReturn',
            'Customers',
            // 'SalesInvoice',
            // 'quotation',
            'DiscountScheam',
            'tax',
            'pos',
            'touchpos',
            // 'billPrintModel',
            'production',
            // 'MakeProductForm',
            // 'ProductModal',
            // 'ProductionCategoryModal',
            // 'RawMaterialModal',
            // 'reports',
            // 'ReportTable',
            'DailyProfit',
            'BillWiseProfit',
            'CompanyWiseProfit', // Not used in any route
            'SupplierWiseProfit',
            'Outstanding',
            'ItemWiseProfit',
            'CustomerWiseProfit',
            'StoreAndLocationWiseProfit',
            'CategoryWiseProfit',
            'Monthly-wise-Report',
            'reports/registry',
            'reports/dailysales',
            // 'financial-accounting',
            'bill-by-bill-collection',
            'aging-analysis',
            'payable',
            'profit-and-loss-report',
            'cash-in-hand',
            'bank-account',
            'balance-sheet',
            'trial-balance',
            // 'voucher',
            'voucher/ReceiveVoucher',
            'voucher/PaymentVoucher',
            // 'ledger',
            'ledger/new-ledger',
            'ledger/statement',
            'Sms-template',
            // 'loyalty',
            'loyalty/report',
            'loyalty/generate-card',
            'loyalty/design-card',
            // 'branch-management',
            'branch-management/create-branch',
            'branch-management/sales-report',
            'branch-management/stock-report',
            'branch-management/stock-transfer',
            'branch-management/receive-stock',
            'settings',
            'CreateCompany',
            'InvoiceTemplate',
            // 'CalculatorModal',
            // 'PERMISSIONS', // Not used in any route
            'StaffManagement',
            // 'StaffRegistration',
            // 'RoleBasedAccessControl',
            // 'AttendanceShiftManagement',
            // 'PayrollSalaryManagement',
            'Approvels',
            // 'TaskManager',
            'HomePage',
            'ProjectsPage',
            'TasksPage',
            'SubtasksPage',
            'ReportPage',
            // 'ProjectForm',
            // 'SubtaskForm',
            // 'TaskForm',
            // 'products', // Not used in any route
            // 'users', // Not used in any route
            // 'Loyalty Card Membership', // Not used in any route
            'promotion-template',
            'outstanding-template',
            'sales-bill-template' // Add any additional unique SideNav/RoleModal paths here
        ];
        
        // Merge all permissions for superadmin
        $totalPaths = array_merge($pagePaths, $superAdminOnlyPaths);

        // Create permissions for all paths
        $permissionIds = [];
        foreach ($totalPaths as $path) {
            $permission = Permission::firstOrCreate([
                'name' => $path,
                'guard_name' => 'api'
            ]);
            $permissionIds[$path] = $permission->id;
            Log::info('Created/Found permission', ['name' => $path, 'id' => $permission->id]);
        }

        // Create roles
        $admin = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'api']);
        Log::info('Created/Found admin role', ['id' => $admin->id]);

        $superadmin = Role::firstOrCreate(['name' => 'superadmin', 'guard_name' => 'api']);
        Log::info('Created/Found superadmin role', ['id' => $superadmin->id]);

        $manager = Role::firstOrCreate(['name' => 'manager', 'guard_name' => 'api']);
        $cashier = Role::firstOrCreate(['name' => 'cashier', 'guard_name' => 'api']);
        $storekeeper = Role::firstOrCreate(['name' => 'storekeeper', 'guard_name' => 'api']);
        $normaluser = Role::firstOrCreate(['name' => 'user', 'guard_name' => 'api']);

        // Assign permissions to roles
        $adminPermissions = Permission::whereIn('name', $pagePaths)->where('guard_name', 'api')->get();
        Log::info('Permissions found for admin role', [
            'count' => $adminPermissions->count(),
            'names' => $adminPermissions->pluck('name')->toArray(),
            'ids' => $adminPermissions->pluck('id')->toArray()
        ]);
        $admin->syncPermissions($adminPermissions);
        $count = DB::table('role_has_permissions')->where('role_id', $admin->id)->count();
        Log::info('Assigned permissions to admin role', ['role_id' => $admin->id, 'permission_count' => $count, 'permissions' => $adminPermissions->pluck('name')->toArray()]);

        $superadminPermissions = Permission::whereIn('name', $totalPaths)->where('guard_name', 'api')->get();
        $superadmin->syncPermissions($superadminPermissions);
        $count = DB::table('role_has_permissions')->where('role_id', $superadmin->id)->count();
        Log::info('Assigned permissions to superadmin role', ['role_id' => $superadmin->id, 'permission_count' => $count, 'permissions' => $superadminPermissions->pluck('name')->toArray()]);

        $manager->syncPermissions(['settings']);
        $cashier->syncPermissions(['settings']);
        $storekeeper->syncPermissions(['settings']);
        $normaluser->syncPermissions(['settings']);

        // Assign roles to users
        $users = [
            ['email' => '<EMAIL>', 'role' => 'admin'],
            ['email' => '<EMAIL>', 'role' => 'admin'],
            ['email' => '<EMAIL>', 'role' => 'superadmin'],
            ['email' => '<EMAIL>', 'role' => 'cashier'],
            ['email' => '<EMAIL>', 'role' => 'manager'],
            ['email' => '<EMAIL>', 'role' => 'cashier'],
        ];

        foreach ($users as $userData) {
            $user = User::where('email', $userData['email'])->first();
            if ($user) {
                $role = Role::where('name', $userData['role'])->first();
                if ($role) {
                    DB::table('model_has_roles')->updateOrInsert(
                        [
                            'model_id' => $user->id,
                            'model_type' => get_class($user),
                            'role_id' => $role->id
                        ],
                        [
                            'model_id' => $user->id,
                            'model_type' => get_class($user),
                            'role_id' => $role->id
                        ]
                    );
                    $user->update(['role' => $userData['role']]);
                    Log::info('Assigned role to user', ['email' => $user->email, 'role' => $userData['role'], 'role_id' => $role->id]);
                } else {
                    Log::error('Role not found', ['role' => $userData['role']]);
                }
            } else {
                Log::warning('User not found for role assignment', ['email' => $userData['email']]);
            }
        }

        Log::info('PermissionSeeder completed successfully');
    }
}