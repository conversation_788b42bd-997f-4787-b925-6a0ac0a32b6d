<?php

namespace App\Http\Controllers;

use App\Models\Companies;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CompaniesController extends Controller
{
    public function index(): JsonResponse
    {
        try {
            $companies = Companies::all();
            return response()->json($companies, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to fetch companies'], 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'company_name' => 'required|string|max:255',
                'address' => 'nullable|string|max:255',
                'contact' => 'nullable|string|max:255',
                'opening_balance' => 'nullable|numeric|min:0',
            ]);

            $company = Companies::create($validated);
            return response()->json($company, 201);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to create company'], 500);
        }
    }

    public function update(Request $request, $id): JsonResponse
    {
        try {
            $company = Companies::findOrFail($id);
            $validated = $request->validate([
                'company_name' => 'required|string|max:255',
                'address' => 'nullable|string|max:255',
                'contact' => 'nullable|string|max:255',
                'opening_balance' => 'nullable|numeric|min:0',
            ]);
            $company->update($validated);
            return response()->json($company, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to update company'], 404);
        }
    }

    public function destroy($id): JsonResponse
    {
        try {
            $company = Companies::findOrFail($id);
            $company->delete();
            return response()->json(['message' => 'Company deleted successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete company'], 404);
        }
    }
}
