{"name": "bacon/bacon-qr-code", "description": "BaconQrCode is a QR code generator for PHP.", "license": "BSD-2-<PERSON><PERSON>", "homepage": "https://github.com/Bacon/BaconQrCode", "require": {"php": "^8.1", "ext-iconv": "*", "dasprid/enum": "^1.0.3"}, "suggest": {"ext-imagick": "to generate QR code images"}, "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "autoload-dev": {"psr-4": {"BaconQrCodeTest\\": "test/"}}, "require-dev": {"phpunit/phpunit": "^10.5.11 || 11.0.4", "spatie/phpunit-snapshot-assertions": "^5.1.5", "squizlabs/php_codesniffer": "^3.9", "phly/keep-a-changelog": "^2.12"}, "config": {"allow-plugins": {"ocramius/package-versions": true, "php-http/discovery": true}}, "archive": {"exclude": ["/test", "/phpunit.xml.dist"]}}