{"name": "brianium/paratest", "description": "Parallel testing for PHP", "license": "MIT", "type": "library", "keywords": ["testing", "PHPUnit", "concurrent", "parallel"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "homepage": "https://github.com/paratestphp/paratest", "funding": [{"type": "github", "url": "https://github.com/sponsors/Slamdunk"}, {"type": "paypal", "url": "https://paypal.me/filippotessarotto"}], "require": {"php": "~8.2.0 || ~8.3.0 || ~8.4.0", "ext-dom": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-simplexml": "*", "fidry/cpu-core-counter": "^1.2.0", "jean85/pretty-package-versions": "^2.1.0", "phpunit/php-code-coverage": "^11.0.9 || ^12.0.4", "phpunit/php-file-iterator": "^5.1.0 || ^6", "phpunit/php-timer": "^7.0.1 || ^8", "phpunit/phpunit": "^11.5.11 || ^12.0.6", "sebastian/environment": "^7.2.0 || ^8", "symfony/console": "^6.4.17 || ^7.2.1", "symfony/process": "^6.4.19 || ^7.2.4"}, "require-dev": {"ext-pcov": "*", "ext-posix": "*", "doctrine/coding-standard": "^12.0.0", "phpstan/phpstan": "^2.1.6", "phpstan/phpstan-deprecation-rules": "^2.0.1", "phpstan/phpstan-phpunit": "^2.0.4", "phpstan/phpstan-strict-rules": "^2.0.3", "squizlabs/php_codesniffer": "^3.11.3", "symfony/filesystem": "^6.4.13 || ^7.2.0"}, "autoload": {"psr-4": {"ParaTest\\": ["src/"]}}, "autoload-dev": {"psr-4": {"ParaTest\\Tests\\": "test/"}}, "bin": ["bin/paratest", "bin/paratest_for_phpstorm"], "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true, "infection/extension-installer": true}, "sort-packages": true}}