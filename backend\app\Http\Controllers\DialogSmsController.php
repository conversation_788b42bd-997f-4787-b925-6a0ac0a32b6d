<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class DialogSmsController extends Controller
{
    public function sendSms(Request $request)
    {
        $validated = $request->validate([
            'messages' => 'required|array|min:1',
            'messages.*.number' => 'required|string',
            'messages.*.text' => 'required|string',
            'messages.*.mask' => 'required|string',
            'messages.*.campaignName' => 'required|string',
            'messages.*.clientRef' => 'nullable|string',
            'messages.*.scheduledTime' => 'nullable|date_format:Y-m-d\TH:i:s',
            'messages.*.defaultName' => 'nullable|string',
            'messages.*.defaultParam1' => 'nullable|string',
            'messages.*.defaultParam2' => 'nullable|string',
            'messages.*.defaultParam3' => 'nullable|string',
            'messages.*.flash' => 'nullable|boolean',
        ]);

        // Use .env values securely
        $username = env('DIALOG_API_USER', 'user_imss');
        $password = env('DIALOG_API_PASSWORD', 'na*%69NY');
        $digest = md5($password);
        $created = now()->format('Y-m-d\TH:i:s');

        try {
            $response = Http::withHeaders([
                'USER' => $username,
                'DIGEST' => $digest,
                'CREATED' => $created,
            ])
            ->asJson() // 🔥 This forces JSON encoding
            ->post('https://richcommunication.dialog.lk/api/sms/send', [
                'messages' => $validated['messages']
            ]);

            $data = $response->json();

            if ($response->successful() && isset($data['resultCode']) && ($data['resultCode'] == 0 || $data['resultCode'] == 100)) {
                return response()->json([
                    'success' => true,
                    'dialogResponse' => $data
                ]);
            }

            return response()->json([
                'success' => false,
                'error' => $data['resultDesc'] ?? 'Unknown error',
                'dialogResponse' => $data
            ], 400);

        } catch (\Throwable $e) {
            return response()->json([
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage()
            ], 500);
        }
    }
}
