<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('quotations', function (Blueprint $table) {
            // Add new nullable string columns for approval details
            $table->string('received_by')->nullable()->after('agent_phone');
            $table->string('prepared_by')->nullable()->after('received_by');
            $table->string('checked_by')->nullable()->after('prepared_by');
            $table->string('authorized_by')->nullable()->after('checked_by');
            $table->string('approved')->nullable()->after('authorized_by');

            // Drop old columns if they exist
            if (Schema::hasColumn('quotations', 'approved_by')) {
                $table->dropColumn('approved_by');
            }
            if (Schema::hasColumn('quotations', 'next_approval_to')) {
                $table->dropColumn('next_approval_to');
            }
            if (Schema::hasColumn('quotations', 'details')) {
                $table->dropColumn('details');
            }
            if (Schema::hasColumn('quotations', 'date_time_approval')) {
                $table->dropColumn('date_time_approval');
            }
        });
    }

    public function down(): void
    {
        Schema::table('quotations', function (Blueprint $table) {
            // Re-add old columns
            $table->string('approved_by')->nullable()->after('total');
            $table->string('next_approval_to')->nullable()->after('approved_by');
            $table->text('details')->nullable()->after('next_approval_to');
            $table->string('date_time_approval')->nullable()->after('details');

            // Drop new columns
            if (Schema::hasColumn('quotations', 'received_by')) {
                $table->dropColumn('received_by');
            }
            if (Schema::hasColumn('quotations', 'prepared_by')) {
                $table->dropColumn('prepared_by');
            }
            if (Schema::hasColumn('quotations', 'checked_by')) {
                $table->dropColumn('checked_by');
            }
            if (Schema::hasColumn('quotations', 'authorized_by')) {
                $table->dropColumn('authorized_by');
            }
            if (Schema::hasColumn('quotations', 'approved')) {
                $table->dropColumn('approved');
            }
        });
    }
};
