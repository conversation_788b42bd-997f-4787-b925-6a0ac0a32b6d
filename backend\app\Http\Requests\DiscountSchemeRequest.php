<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DiscountSchemeRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on your authorization logic
    }

    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', Rule::in(['percentage', 'amount', 'free'])],
            'value' => ['nullable', 'numeric', 'min:0'],
            'applies_to' => ['required', Rule::in(['product', 'category', 'customerGroup'])],
            'target' => ['required', 'string', 'max:255'],
            'start_date' => ['nullable', 'date'],
            'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
            'active' => ['boolean'],
        ];

        if ($this->input('type') === 'free') {
            $rules['buy_quantity'] = ['required', 'integer', 'min:1'];
            $rules['free_quantity'] = ['required', 'integer', 'min:1'];
            $rules['value'] = ['nullable']; // Not used for 'free' type
        } else {
            $rules['buy_quantity'] = ['nullable'];
            $rules['free_quantity'] = ['nullable'];
            $rules['value'] = ['required', 'numeric', 'min:0'];
        }

        return $rules;
    }
}