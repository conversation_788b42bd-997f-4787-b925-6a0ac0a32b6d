{"name": "phpdocumentor/type-resolver", "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0", "doctrine/deprecations": "^1.0"}, "require-dev": {"ext-tokenizer": "*", "phpunit/phpunit": "^9.5", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/extension-installer": "^1.1", "vimeo/psalm": "^4.25", "rector/rector": "^0.13.9", "phpbench/phpbench": "^1.2"}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "autoload-dev": {"psr-4": {"phpDocumentor\\Reflection\\": ["tests/unit", "tests/benchmark"]}}, "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "config": {"platform": {"php": "7.3.0"}, "allow-plugins": {"phpstan/extension-installer": true}}}