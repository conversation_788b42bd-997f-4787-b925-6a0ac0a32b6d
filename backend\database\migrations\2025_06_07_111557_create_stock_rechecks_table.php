<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStockRechecksTable extends Migration
{
    public function up()
    {
        Schema::create('stock_rechecks', function (Blueprint $table) {
            $table->id();
            $table->string('item_code');
            $table->string('item_name');
            $table->integer('system_qty');
            $table->integer('store_qty');
            $table->integer('difference');
            $table->string('location');
            $table->text('remarks')->nullable();
            $table->string('status');
            $table->boolean('update_actual_stock')->default(false);
            $table->integer('corrected_closing_stock')->nullable();  // New column for corrected closing stock
            $table->unsignedBigInteger('user_id')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('item_code')->references('item_code')->on('products')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('stock_rechecks');
    }
}