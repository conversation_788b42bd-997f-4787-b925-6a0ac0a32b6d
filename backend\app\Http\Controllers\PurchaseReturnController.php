<?php

namespace App\Http\Controllers;

use App\Models\PurchaseReturn;
use App\Models\PurchaseReturnItem;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\PurchaseItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PurchaseReturnController extends Controller
{
    /**
     * Get all suppliers.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSuppliers()
    {
        $suppliers = Supplier::select('id', 'supplier_name')->get();
        return response()->json(['data' => $suppliers], 200);
    }

    /**
     * Get all products with relevant fields.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProducts()
    {
        $products = Product::select('product_id', 'product_name', 'buying_cost', 'item_code', 'barcode')->get();
        return response()->json(['data' => $products], 200);
    }

    /**
     * Get all purchase returns with related data.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPurchaseReturns()
    {
        $purchaseReturns = PurchaseReturn::with(['supplier', 'items.product'])->get();
        $purchaseReturns->each(function ($return) {
            $return->items->each(function ($item) {
                $item->buying_cost = (float) $item->buying_cost;
            });
        });
        return response()->json(['data' => $purchaseReturns], 200);
    }

    /**
     * Get a single purchase return by ID.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $purchaseReturn = PurchaseReturn::with(['supplier', 'items.product'])->findOrFail($id);
            $purchaseReturn->items->each(function ($item) {
                $item->buying_cost = (float) $item->buying_cost;
            });
            return response()->json(['data' => $purchaseReturn], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Purchase return not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Create a new purchase return.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPurchaseReturn(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'supplier_id' => 'required|exists:suppliers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,product_id',
            'items.*.product_variant_id' => 'nullable|integer', // Batch tracking
            'items.*.batch_number' => 'nullable|string|max:255', // Batch tracking
            'items.*.expiry_date' => 'nullable|string', // Batch tracking
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.buying_cost' => 'required|numeric|min:0',
            'items.*.reason' => 'nullable|string|max:255',
            'refund_method' => 'required|in:cash,bank,credit',
            'remarks' => 'nullable|string|max:1000',
            'status' => 'required|in:pending,approved,rejected',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $purchaseReturn = PurchaseReturn::create([
                'invoice_number' => PurchaseReturn::generateInvoiceNumber(),
                'supplier_id' => $request->supplier_id,
                'refund_method' => $request->refund_method,
                'remarks' => $request->remarks,
                'status' => $request->status,
            ]);

            foreach ($request->items as $item) {
                $product = Product::where('product_id', $item['product_id'])->first();
                if (!$product) {
                    throw new \Exception("Product with ID {$item['product_id']} not found.");
                }
                // Handle expiry date conversion
                $expiryDate = null;
                if (!empty($item['expiry_date'])) {
                    try {
                        $expiryDate = date('Y-m-d', strtotime($item['expiry_date']));
                    } catch (\Exception $e) {
                        $expiryDate = null;
                    }
                }

                PurchaseReturnItem::create([
                    'purchase_return_id' => $purchaseReturn->id,
                    'product_id' => $item['product_id'],
                    'product_variant_id' => $item['product_variant_id'] ?? null, // Batch tracking
                    'batch_number' => $item['batch_number'] ?? null, // Batch tracking
                    'expiry_date' => $expiryDate, // Batch tracking
                    'product_name' => $product->product_name,
                    'quantity' => $item['quantity'],
                    'buying_cost' => $item['buying_cost'],
                    'reason' => $item['reason'] ?: null,
                ]);

                // If status is approved, update purchase quantity with batch information
                if ($request->status === 'approved') {
                    $this->updatePurchaseOnApproval(
                        $item['product_id'],
                        $item['quantity'],
                        $item['product_variant_id'] ?? null,
                        $item['batch_number'] ?? null
                    );
                }
            }

            DB::commit();
            
            // Log user activity AFTER commit
            $userId = auth()->id();
            if ($userId) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $userId,
                    'Purchase Return Created',
                    'Purchase Returns',
                    $purchaseReturn->id,
                    [
                        'invoice_number' => $purchaseReturn->invoice_number,
                        'supplier_id' => $purchaseReturn->supplier_id,
                        'refund_method' => $purchaseReturn->refund_method,
                        'status' => $purchaseReturn->status
                    ]
                );
            } else {
                \Log::error('Cannot log Purchase Return Created - no authenticated user found');
            }
            
            return response()->json([
                'message' => 'Purchase return created successfully',
                'data' => $purchaseReturn->load('items', 'supplier')
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error creating purchase return',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing purchase return.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'supplier_id' => 'required|exists:suppliers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,product_id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.buying_cost' => 'required|numeric|min:0',
            'items.*.reason' => 'nullable|string|max:255',
            'refund_method' => 'required|in:cash,bank,credit',
            'remarks' => 'nullable|string|max:1000',
            'status' => 'required|in:pending,approved,rejected',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $purchaseReturn = PurchaseReturn::findOrFail($id);
            $oldStatus = $purchaseReturn->status;

            // If status is changing from approved to something else, revert purchase changes
            if ($oldStatus === 'approved' && $request->status !== 'approved') {
                $oldItems = PurchaseReturnItem::where('purchase_return_id', $id)->get();
                foreach ($oldItems as $item) {
                    $this->revertPurchaseOnApproval(
                        $item->product_id,
                        $item->quantity,
                        $item->product_variant_id,
                        $item->batch_number
                    );
                }
            }

            $purchaseReturn->update([
                'supplier_id' => $request->supplier_id,
                'refund_method' => $request->refund_method,
                'remarks' => $request->remarks,
                'status' => $request->status,
            ]);

            // Delete existing items and create new ones
            PurchaseReturnItem::where('purchase_return_id', $purchaseReturn->id)->delete();
            foreach ($request->items as $item) {
                $product = Product::where('product_id', $item['product_id'])->first();
                if (!$product) {
                    throw new \Exception("Product with ID {$item['product_id']} not found.");
                }
                // Handle expiry date conversion
                $expiryDate = null;
                if (!empty($item['expiry_date'])) {
                    try {
                        $expiryDate = date('Y-m-d', strtotime($item['expiry_date']));
                    } catch (\Exception $e) {
                        $expiryDate = null;
                    }
                }

                PurchaseReturnItem::create([
                    'purchase_return_id' => $purchaseReturn->id,
                    'product_id' => $item['product_id'],
                    'product_variant_id' => $item['product_variant_id'] ?? null, // Batch tracking
                    'batch_number' => $item['batch_number'] ?? null, // Batch tracking
                    'expiry_date' => $expiryDate, // Batch tracking
                    'product_name' => $product->product_name,
                    'quantity' => $item['quantity'],
                    'buying_cost' => $item['buying_cost'],
                    'reason' => $item['reason'] ?: null,
                ]);

                // If new status is approved, update purchase quantity with batch information
                if ($request->status === 'approved' && $oldStatus !== 'approved') {
                    $this->updatePurchaseOnApproval(
                        $item['product_id'],
                        $item['quantity'],
                        $item['product_variant_id'] ?? null,
                        $item['batch_number'] ?? null
                    );
                }
            }

            DB::commit();
            
            // Log user activity AFTER commit
            $userId = auth()->id();
            if ($userId) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $userId,
                    'Purchase Return Updated',
                    'Purchase Returns',
                    $purchaseReturn->id,
                    [
                        'invoice_number' => $purchaseReturn->invoice_number,
                        'supplier_id' => $purchaseReturn->supplier_id,
                        'refund_method' => $purchaseReturn->refund_method,
                        'status' => $purchaseReturn->status
                    ]
                );
            } else {
                \Log::error('Cannot log Purchase Return Updated - no authenticated user found');
            }
            
            return response()->json([
                'message' => 'Purchase return updated successfully',
                'data' => $purchaseReturn->load('items', 'supplier')
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error updating purchase return',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a purchase return.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            $purchaseReturn = PurchaseReturn::findOrFail($id);
            if ($purchaseReturn->status === 'approved') {
                $items = PurchaseReturnItem::where('purchase_return_id', $id)->get();
                foreach ($items as $item) {
                    $this->revertPurchaseOnApproval(
                        $item->product_id,
                        $item->quantity,
                        $item->product_variant_id,
                        $item->batch_number
                    );
                }
            }

            $purchaseReturn->delete();
            DB::commit();
            
            // Log user activity AFTER commit
            $userId = auth()->id();
            if ($userId) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $userId,
                    'Purchase Return Deleted',
                    'Purchase Returns',
                    $purchaseReturn->id,
                    [
                        'invoice_number' => $purchaseReturn->invoice_number,
                        'supplier_id' => $purchaseReturn->supplier_id,
                        'refund_method' => $purchaseReturn->refund_method,
                        'status' => $purchaseReturn->status,
                        'deleted_by' => $userId
                    ]
                );
            } else {
                \Log::error('Cannot log Purchase Return Deleted - no authenticated user found');
            }
            
            return response()->json([
                'message' => 'Purchase return deleted successfully'
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error deleting purchase return',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update purchase quantity when a purchase return is approved using batch-specific matching.
     * This handles returns from specific batches, not just FIFO by product.
     *
     * @param int $productId
     * @param int $quantity
     * @param int|null $productVariantId
     * @param string|null $batchNumber
     * @return void
     */
    private function updatePurchaseOnApproval($productId, $quantity, $productVariantId = null, $batchNumber = null)
    {
        try {
            $remainingQuantity = $quantity;

            // Build query for purchase items
            $query = PurchaseItem::where('product_id', $productId)
                ->where('quantity', '>', 0); // Only items with remaining quantity

            // If batch information is provided, match specific batch
            if ($productVariantId) {
                $query->where('product_variant_id', $productVariantId);
            }
            if ($batchNumber) {
                $query->where('batch_number', $batchNumber);
            }

            // Order by FIFO (oldest first) within the matching batch
            $purchaseItems = $query->orderBy('created_at', 'asc')->get();

            Log::info("Processing return for product ID {$productId}, quantity {$quantity}");
            if ($productVariantId) {
                Log::info("Matching specific variant ID: {$productVariantId}");
            }
            if ($batchNumber) {
                Log::info("Matching specific batch: {$batchNumber}");
            }
            Log::info("Found " . $purchaseItems->count() . " matching purchase items with remaining quantity");

            foreach ($purchaseItems as $purchaseItem) {
                if ($remainingQuantity <= 0) {
                    break; // All return quantity has been processed
                }

                $availableQuantity = $purchaseItem->quantity;
                $quantityToReduce = min($remainingQuantity, $availableQuantity);

                // Reduce the purchased quantity
                $newQuantity = $availableQuantity - $quantityToReduce;
                $purchaseItem->update(['quantity' => $newQuantity]);

                $remainingQuantity -= $quantityToReduce;

                Log::info("Updated PurchaseItem ID {$purchaseItem->id}: reduced by {$quantityToReduce}, new quantity: {$newQuantity}");
            }

            if ($remainingQuantity > 0) {
                Log::warning("Could not process full return quantity. Remaining: {$remainingQuantity} for product ID {$productId}");
            } else {
                Log::info("Successfully processed full return quantity for product ID {$productId}");
            }
        } catch (\Exception $e) {
            Log::error("Error updating purchase quantity for product ID {$productId}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Revert purchase quantity changes when a purchase return is unapproved or deleted.
     * This uses batch-specific matching to properly restore quantities.
     *
     * @param int $productId
     * @param int $quantity
     * @param int|null $productVariantId
     * @param string|null $batchNumber
     * @return void
     */
    private function revertPurchaseOnApproval($productId, $quantity, $productVariantId = null, $batchNumber = null)
    {
        try {
            $remainingQuantity = $quantity;

            // Build query for purchase items
            $query = PurchaseItem::where('product_id', $productId);

            // If batch information is provided, match specific batch
            if ($productVariantId) {
                $query->where('product_variant_id', $productVariantId);
            }
            if ($batchNumber) {
                $query->where('batch_number', $batchNumber);
            }

            // Order by reverse FIFO (newest first for revert) within the matching batch
            $purchaseItems = $query->orderBy('created_at', 'desc')->get();

            Log::info("Reverting return for product ID {$productId}, quantity {$quantity}");
            if ($productVariantId) {
                Log::info("Reverting specific variant ID: {$productVariantId}");
            }
            if ($batchNumber) {
                Log::info("Reverting specific batch: {$batchNumber}");
            }
            Log::info("Found " . $purchaseItems->count() . " matching purchase items for revert");

            foreach ($purchaseItems as $purchaseItem) {
                if ($remainingQuantity <= 0) {
                    break; // All revert quantity has been processed
                }

                // Add back the quantity (reverse the reduction)
                $quantityToAdd = min($remainingQuantity, $quantity); // Add back what was reduced
                $newQuantity = $purchaseItem->quantity + $quantityToAdd;
                $purchaseItem->update(['quantity' => $newQuantity]);

                $remainingQuantity -= $quantityToAdd;

                Log::info("Reverted PurchaseItem ID {$purchaseItem->id}: added back {$quantityToAdd}, new quantity: {$newQuantity}");

                // For revert, we typically add back to the most recent items first
                break; // Add to the most recent purchase item
            }

            if ($remainingQuantity > 0) {
                Log::warning("Could not revert full quantity. Remaining: {$remainingQuantity} for product ID {$productId}");
            } else {
                Log::info("Successfully reverted full quantity for product ID {$productId}");
            }
        } catch (\Exception $e) {
            Log::error("Error reverting purchase quantity for product ID {$productId}: " . $e->getMessage());
            throw $e;
        }
    }
}