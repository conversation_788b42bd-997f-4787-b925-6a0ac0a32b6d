<?php

namespace <PERSON><PERSON>\Fortify\Http\Responses;

use <PERSON><PERSON>\Fortify\Contracts\LoginResponse as LoginResponseContract;
use <PERSON><PERSON>\Fortify\Fortify;

class LoginResponse implements LoginResponseContract
{
    /**
     * Create an HTTP response that represents the object.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function toResponse($request)
    {
        return $request->wantsJson()
                    ? response()->json(['two_factor' => false])
                    : redirect()->intended(Fortify::redirects('login'));
    }
}
