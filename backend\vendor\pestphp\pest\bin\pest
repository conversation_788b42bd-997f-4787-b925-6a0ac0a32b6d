#!/usr/bin/env php
<?php

declare(strict_types=1);

use Pest\Kernel;
use Pest\Panic;
use Pest\TestCaseFilters\GitDirtyTestCaseFilter;
use Pest\TestCaseMethodFilters\AssigneeTestCaseFilter;
use Pest\TestCaseMethodFilters\IssueTestCaseFilter;
use Pest\TestCaseMethodFilters\NotesTestCaseFilter;
use Pest\TestCaseMethodFilters\PrTestCaseFilter;
use Pest\TestCaseMethodFilters\TodoTestCaseFilter;
use Pest\TestSuite;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Output\ConsoleOutput;

(static function () {
    // Ensures Collision's Printer is registered.
    $_SERVER['COLLISION_PRINTER'] = 'DefaultPrinter';

    $arguments = $originalArguments = $_SERVER['argv'];

    $dirty = false;
    $todo = false;
    $notes = false;

    foreach ($arguments as $key => $value) {

        if ($value === '--compact') {
            $_SERVER['COLLISION_PRINTER_COMPACT'] = 'true';
            unset($arguments[$key]);
        }

        if ($value === '--profile') {
            $_SERVER['COLLISION_PRINTER_PROFILE'] = 'true';
            unset($arguments[$key]);
        }

        if (str_contains($value, '--test-directory=')) {
            unset($arguments[$key]);
        } elseif ($value === '--test-directory') {
            unset($arguments[$key]);

            if (isset($arguments[$key + 1])) {
                unset($arguments[$key + 1]);
            }
        }

        if ($value === '--dirty') {
            $dirty = true;
            unset($arguments[$key]);
        }

        if (in_array($value, ['--todo', '--todos'], true)) {
            $todo = true;
            unset($arguments[$key]);
        }

        if ($value === '--notes') {
            $notes = true;
            unset($arguments[$key]);
        }

        if (str_contains($value, '--assignee=')) {
            unset($arguments[$key]);
        } elseif ($value === '--assignee') {
            unset($arguments[$key]);

            if (isset($arguments[$key + 1])) {
                unset($arguments[$key + 1]);
            }
        }

        if (str_contains($value, '--issue=')) {
            unset($arguments[$key]);
        } elseif ($value === '--issue') {
            unset($arguments[$key]);

            if (isset($arguments[$key + 1])) {
                unset($arguments[$key + 1]);
            }
        }

        if (str_contains($value, '--ticket=')) {
            unset($arguments[$key]);
        } elseif ($value === '--ticket') {
            unset($arguments[$key]);

            if (isset($arguments[$key + 1])) {
                unset($arguments[$key + 1]);
            }
        }

        if (str_contains($value, '--pr=')) {
            unset($arguments[$key]);
        } elseif ($value === '--pr') {
            unset($arguments[$key]);

            if (isset($arguments[$key + 1])) {
                unset($arguments[$key + 1]);
            }
        }

        if (str_contains($value, '--pull-request=')) {
            unset($arguments[$key]);
        } elseif ($value === '--pull-request') {
            unset($arguments[$key]);

            if (isset($arguments[$key + 1])) {
                unset($arguments[$key + 1]);
            }
        }

        if (str_contains($value, '--teamcity')) {
            unset($arguments[$key]);
            $arguments[] = '--no-output';
            unset($_SERVER['COLLISION_PRINTER']);
        }
    }

    // Used when Pest is required using composer.
    $vendorPath = dirname(__DIR__, 4).'/vendor/autoload.php';

    // Used when Pest maintainers are running Pest tests.
    $localPath = dirname(__DIR__).'/vendor/autoload.php';

    if (file_exists($vendorPath)) {
        include_once $vendorPath;
        $autoloadPath = $vendorPath;
    } else {
        include_once $localPath;
        $autoloadPath = $localPath;
    }

    // Get $rootPath based on $autoloadPath
    $rootPath = dirname($autoloadPath, 2);
    $input = new ArgvInput;

    $testSuite = TestSuite::getInstance(
        $rootPath,
        $input->getParameterOption('--test-directory', 'tests'),
    );

    if ($dirty) {
        $testSuite->tests->addTestCaseFilter(new GitDirtyTestCaseFilter($rootPath));
    }

    if ($todo) {
        $testSuite->tests->addTestCaseMethodFilter(new TodoTestCaseFilter);
    }

    if ($notes) {
        $testSuite->tests->addTestCaseMethodFilter(new NotesTestCaseFilter);
    }

    if ($assignee = $input->getParameterOption('--assignee')) {
        $testSuite->tests->addTestCaseMethodFilter(new AssigneeTestCaseFilter((string) $assignee));
    }

    if ($issue = $input->getParameterOption('--issue')) {
        $testSuite->tests->addTestCaseMethodFilter(new IssueTestCaseFilter((int) $issue));
    }

    if ($issue = $input->getParameterOption('--ticket')) {
        $testSuite->tests->addTestCaseMethodFilter(new IssueTestCaseFilter((int) $issue));
    }

    if ($pr = $input->getParameterOption('--pr')) {
        $testSuite->tests->addTestCaseMethodFilter(new PrTestCaseFilter((int) $pr));
    }

    if ($pr = $input->getParameterOption('--pull-request')) {
        $testSuite->tests->addTestCaseMethodFilter(new PrTestCaseFilter((int) $pr));
    }

    $isDecorated = $input->getParameterOption('--colors', 'always') !== 'never';

    $output = new ConsoleOutput(ConsoleOutput::VERBOSITY_NORMAL, $isDecorated);

    try {
        $kernel = Kernel::boot($testSuite, $input, $output);

        $result = $kernel->handle($originalArguments, $arguments);

        $kernel->terminate();
    } catch (Throwable|Error $e) {
        Panic::with($e);
    }

    exit($result);
})();
