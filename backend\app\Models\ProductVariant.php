<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductVariant extends Model
{
    use HasFactory, SoftDeletes;

    protected $primaryKey = 'product_variant_id';

    protected $fillable = [
        'product_id',
        'batch_number',
        'expiry_date',
        'buying_cost',
        'sales_price',
        'minimum_price',
        'wholesale_price',
        'barcode',
        'mrp',
        'minimum_stock_quantity',
        'opening_stock_quantity',
        'closing_stock_quantity',
        'opening_stock_value',
        'store_location',
        'cabinet',
        'row',
        'extra_fields',
    ];

    protected $casts = [
        'buying_cost' => 'float',
        'sales_price' => 'float',
        'minimum_price' => 'float',
        'wholesale_price' => 'float',
        'mrp' => 'float',
        'minimum_stock_quantity' => 'float',
        'opening_stock_quantity' => 'float',
        'closing_stock_quantity' => 'float',
        'opening_stock_value' => 'float',
        'extra_fields' => 'array',
        'expiry_date' => 'date',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'product_id');
    }

    public function deletedByUser()
    {
        return $this->belongsTo(User::class, 'deleted_by')->withTrashed();
    }
}
