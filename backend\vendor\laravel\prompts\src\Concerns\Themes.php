<?php

namespace <PERSON><PERSON>\Prompts\Concerns;

use InvalidArgumentException;
use <PERSON><PERSON>\Prompts\Clear;
use <PERSON><PERSON>\Prompts\ConfirmPrompt;
use <PERSON><PERSON>\Prompts\MultiSearchPrompt;
use <PERSON><PERSON>\Prompts\MultiSelectPrompt;
use <PERSON><PERSON>\Prompts\Note;
use <PERSON><PERSON>\Prompts\PasswordPrompt;
use <PERSON><PERSON>\Prompts\PausePrompt;
use Lara<PERSON>\Prompts\Progress;
use <PERSON><PERSON>\Prompts\SearchPrompt;
use Lara<PERSON>\Prompts\SelectPrompt;
use <PERSON><PERSON>\Prompts\Spinner;
use Lara<PERSON>\Prompts\SuggestPrompt;
use <PERSON><PERSON>\Prompts\Table;
use <PERSON><PERSON>\Prompts\TextareaPrompt;
use <PERSON><PERSON>\Prompts\TextPrompt;
use Lara<PERSON>\Prompts\Themes\Default\ClearRenderer;
use <PERSON><PERSON>\Prompts\Themes\Default\ConfirmPromptRenderer;
use Lara<PERSON>\Prompts\Themes\Default\MultiSearchPromptRenderer;
use Lara<PERSON>\Prompts\Themes\Default\MultiSelectPromptRenderer;
use Lara<PERSON>\Prompts\Themes\Default\NoteRenderer;
use <PERSON><PERSON>\Prompts\Themes\Default\PasswordPromptRenderer;
use Lara<PERSON>\Prompts\Themes\Default\PausePromptRenderer;
use Laravel\Prompts\Themes\Default\ProgressRenderer;
use Laravel\Prompts\Themes\Default\SearchPromptRenderer;
use Laravel\Prompts\Themes\Default\SelectPromptRenderer;
use Laravel\Prompts\Themes\Default\SpinnerRenderer;
use Laravel\Prompts\Themes\Default\SuggestPromptRenderer;
use Laravel\Prompts\Themes\Default\TableRenderer;
use Laravel\Prompts\Themes\Default\TextareaPromptRenderer;
use Laravel\Prompts\Themes\Default\TextPromptRenderer;

trait Themes
{
    /**
     * The name of the active theme.
     */
    protected static string $theme = 'default';

    /**
     * The available themes.
     *
     * @var array<string, array<class-string<\Laravel\Prompts\Prompt>, class-string<object&callable>>>
     */
    protected static array $themes = [
        'default' => [
            TextPrompt::class => TextPromptRenderer::class,
            TextareaPrompt::class => TextareaPromptRenderer::class,
            PasswordPrompt::class => PasswordPromptRenderer::class,
            SelectPrompt::class => SelectPromptRenderer::class,
            MultiSelectPrompt::class => MultiSelectPromptRenderer::class,
            ConfirmPrompt::class => ConfirmPromptRenderer::class,
            PausePrompt::class => PausePromptRenderer::class,
            SearchPrompt::class => SearchPromptRenderer::class,
            MultiSearchPrompt::class => MultiSearchPromptRenderer::class,
            SuggestPrompt::class => SuggestPromptRenderer::class,
            Spinner::class => SpinnerRenderer::class,
            Note::class => NoteRenderer::class,
            Table::class => TableRenderer::class,
            Progress::class => ProgressRenderer::class,
            Clear::class => ClearRenderer::class,
        ],
    ];

    /**
     * Get or set the active theme.
     *
     * @throws \InvalidArgumentException
     */
    public static function theme(?string $name = null): string
    {
        if ($name === null) {
            return static::$theme;
        }

        if (! isset(static::$themes[$name])) {
            throw new InvalidArgumentException("Prompt theme [{$name}] not found.");
        }

        return static::$theme = $name;
    }

    /**
     * Add a new theme.
     *
     * @param  array<class-string<\Laravel\Prompts\Prompt>, class-string<object&callable>>  $renderers
     */
    public static function addTheme(string $name, array $renderers): void
    {
        if ($name === 'default') {
            throw new InvalidArgumentException('The default theme cannot be overridden.');
        }

        static::$themes[$name] = $renderers;
    }

    /**
     * Get the renderer for the current prompt.
     */
    protected function getRenderer(): callable
    {
        $class = get_class($this);

        return new (static::$themes[static::$theme][$class] ?? static::$themes['default'][$class])($this);
    }

    /**
     * Render the prompt using the active theme.
     */
    protected function renderTheme(): string
    {
        $renderer = $this->getRenderer();

        return $renderer($this);
    }
}
