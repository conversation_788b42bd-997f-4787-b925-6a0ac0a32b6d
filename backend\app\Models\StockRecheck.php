<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockRecheck extends Model
{
    use HasFactory;

    protected $fillable = [
        'item_code',
        'item_name',
        'system_qty',
        'store_qty',
        'difference',
        'location',
        'batch_number',
        'product_variant_id',
        'expiry_date',
        'recheck_type',
        'remarks',
        'status',
        'update_actual_stock',
        'corrected_closing_stock',
        'user_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'item_code', 'item_code');
    }

    public function productVariant()
    {
        return $this->belongsTo(ProductVariant::class, 'product_variant_id', 'product_variant_id');
    }
}