<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\UserLog;
use App\Models\User;
use Carbon\Carbon;

class UserActivitySeeder extends Seeder
{
    public function run()
    {
        $users = User::all();
        
        if ($users->isEmpty()) {
            $this->command->info('No users found. Please create users first.');
            return;
        }

        $actions = [
            'User Login',
            'User Logout',
            'Product Created',
            'Product Updated',
            'Product Deleted',
            'Sale Created',
            'Sale Updated',
            'Customer Created',
            'Customer Updated',
            'Report Generated',
            'Data Exported',
            'Settings Updated'
        ];

        $modules = [
            'Authentication',
            'Products',
            'Sales',
            'Customers',
            'Reports',
            'Settings',
            'Inventory',
            'Users'
        ];

        $sampleData = [];

        // Generate sample data for the last 30 days
        for ($i = 0; $i < 100; $i++) {
            $user = $users->random();
            $action = $actions[array_rand($actions)];
            $module = $modules[array_rand($modules)];
            $date = Carbon::now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59));

            $sampleData[] = [
                'user_id' => $user->id,
                'action' => $action,
                'ip_address' => '192.168.1.' . rand(1, 255),
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'module' => $module,
                'details' => json_encode([
                    'user_email' => $user->email,
                    'user_role' => $user->role,
                    'timestamp' => $date->toISOString()
                ]),
                'created_at' => $date,
                'updated_at' => $date,
            ];
        }

        // Insert sample data
        UserLog::insert($sampleData);

        $this->command->info('Sample user activity data created successfully!');
    }
} 