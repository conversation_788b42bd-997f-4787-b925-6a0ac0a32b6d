<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('company')->nullable()->after('row');
            $table->string('branch_name')->nullable()->after('company');
            $table->integer('branch_qty')->nullable()->after('branch_name');
        });
    }

    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn(['company', 'branch_name', 'branch_qty']);
        });
    }
};
