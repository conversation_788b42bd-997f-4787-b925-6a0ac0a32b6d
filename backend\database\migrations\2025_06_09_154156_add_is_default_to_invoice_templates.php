<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsDefaultToInvoiceTemplates extends Migration
{
    public function up()
    {
        Schema::table('invoice_templates', function (Blueprint $table) {
            $table->boolean('is_default')->default(false)->after('type');
        });
    }

    public function down()
    {
        Schema::table('invoice_templates', function (Blueprint $table) {
            $table->dropColumn('is_default');
        });
    }
}