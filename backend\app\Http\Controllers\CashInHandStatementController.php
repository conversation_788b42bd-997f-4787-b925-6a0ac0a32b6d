<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StaffLedger;
use App\Models\PaymentMethod;
use App\Models\Payment;
use App\Models\AccountSubGroup;
use App\Models\SalesReturn;
use App\Models\SalesReturnItem;
use App\Models\PurchaseReturn;
use App\Models\PurchaseReturnItem;
use Carbon\Carbon;

class CashInHandStatementController extends Controller
{
    public function getStatement(Request $request)
    {
        $fromDate = $request->input('from_date') ? Carbon::parse($request->input('from_date'))->startOfDay() : Carbon::now()->startOfYear();
        $toDate = $request->input('to_date') ? Carbon::parse($request->input('to_date'))->endOfDay() : Carbon::now()->endOfDay();

        // Opening balance for all cash in hand ledgers and its subgroups
        $subGroups = AccountSubGroup::where('main_group', 'Cash in Hand')->pluck('sub_group_name')->toArray();
        $groups = array_merge(['Cash in Hand'], $subGroups);
        $openingBalance = StaffLedger::whereIn('account_group', $groups)->sum('opening_balance');

        // Income: sales/invoice (cash) from PaymentMethod
        $incomeSales = PaymentMethod::whereIn('type', ['sales', 'invoice'])
            ->where('payment_type', 'cash')
            ->whereBetween('date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->date ? $t->date->format('Y-m-d') : null,
                    'type' => ucfirst($t->type),
                    'description' => $t->type . ' #' . ($t->reference_number ?? ''),
                    'amount' => (float) $t->total,
                ];
            });

        // Income: sales/invoice (credit settled amounts) from PaymentMethod
        $incomeSalesCredit = PaymentMethod::whereIn('type', ['sales', 'invoice'])
            ->where('payment_type', 'credit')
            ->whereBetween('date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->date ? $t->date->format('Y-m-d') : null,
                    'type' => ucfirst($t->type) . ' (Credit Settled)',
                    'description' => $t->type . ' #' . ($t->reference_number ?? '') . ' (Credit Settled)',
                    'amount' => (float) $t->settled_amount,
                ];
            })
            ->filter(function($t) {
                return $t['amount'] > 0; // Only include if settled amount > 0
            });
        // Income: receive voucher (cash) from Payment
        $incomeReceiveVouchers = Payment::where('payment_method', 'cash')
            ->where('voucher_no', 'like', 'REC-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->payment_date ? $t->payment_date->format('Y-m-d') : null,
                    'type' => 'Receive Voucher',
                    'description' => 'Receive Voucher #' . ($t->voucher_no ?? ''),
                    'amount' => (float) $t->amount,
                ];
            });

        // Income: receive voucher (credit settled amounts) from Payment
        $incomeReceiveVouchersCredit = Payment::where('payment_method', 'credit')
            ->where('voucher_no', 'like', 'REC-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->payment_date ? $t->payment_date->format('Y-m-d') : null,
                    'type' => 'Receive Voucher (Credit Settled)',
                    'description' => 'Receive Voucher #' . ($t->voucher_no ?? '') . ' (Credit Settled)',
                    'amount' => (float) $t->settled_amount,
                ];
            })
            ->filter(function($t) {
                return $t['amount'] > 0; // Only include if settled amount > 0
            });

        $income = $incomeSales->concat($incomeSalesCredit)->concat($incomeReceiveVouchers)->concat($incomeReceiveVouchersCredit)->sortBy('date')->values();

        // Expense: purchase (cash) from PaymentMethod
        $expensePurchases = PaymentMethod::where('type', 'purchase')
            ->where('payment_type', 'cash')
            ->whereBetween('date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->date ? $t->date->format('Y-m-d') : null,
                    'type' => 'Purchase',
                    'description' => 'Purchase #' . ($t->reference_number ?? ''),
                    'amount' => (float) $t->total,
                ];
            });

        // Expense: purchase (credit settled amounts) from PaymentMethod
        $expensePurchasesCredit = PaymentMethod::where('type', 'purchase')
            ->where('payment_type', 'credit')
            ->whereBetween('date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->date ? $t->date->format('Y-m-d') : null,
                    'type' => 'Purchase (Credit Settled)',
                    'description' => 'Purchase #' . ($t->reference_number ?? '') . ' (Credit Settled)',
                    'amount' => (float) $t->settled_amount,
                ];
            })
            ->filter(function($t) {
                return $t['amount'] > 0; // Only include if settled amount > 0
            });
        // Expense: payment voucher (cash) from Payment
        $expensePaymentVouchers = Payment::where('payment_method', 'cash')
            ->where('voucher_no', 'like', 'PAY-%')
            ->whereBetween('payment_date', [$fromDate, $toDate])
            ->get()
            ->map(function($t) {
                return [
                    'date' => $t->payment_date ? $t->payment_date->format('Y-m-d') : null,
                    'type' => 'Payment Voucher',
                    'description' => 'Payment Voucher #' . ($t->voucher_no ?? ''),
                    'amount' => (float) $t->amount,
                ];
            });
        $expense = $expensePurchases->concat($expensePurchasesCredit)->concat($expensePaymentVouchers)->sortBy('date')->values();

        // SALES RETURN (cash, approved) as EXPENSE
        $salesReturns = SalesReturn::where('refund_method', 'cash')
            ->where('status', 'approved')
            ->whereBetween('created_at', [$fromDate, $toDate])
            ->get();
        foreach ($salesReturns as $return) {
            $amount = SalesReturnItem::where('sales_return_id', $return->id)
                ->get()
                ->sum(function($item) { return $item->quantity * $item->selling_cost; });
            if ($amount > 0) {
                $expense->push([
                    'date' => $return->created_at ? $return->created_at->format('Y-m-d') : null,
                    'type' => 'Sales Return',
                    'description' => 'Sales Return #' . ($return->sales_return_number ?? ''),
                    'amount' => (float) $amount,
                ]);
            }
        }

        // PURCHASE RETURN (cash, approved) as INCOME
        $purchaseReturns = PurchaseReturn::where('refund_method', 'cash')
            ->where('status', 'approved')
            ->whereBetween('created_at', [$fromDate, $toDate])
            ->get();
        foreach ($purchaseReturns as $return) {
            $amount = PurchaseReturnItem::where('purchase_return_id', $return->id)
                ->get()
                ->sum(function($item) { return $item->quantity * $item->buying_cost; });
            if ($amount > 0) {
                $income->push([
                    'date' => $return->created_at ? $return->created_at->format('Y-m-d') : null,
                    'type' => 'Purchase Return',
                    'description' => 'Purchase Return #' . ($return->invoice_number ?? ''),
                    'amount' => (float) $amount,
                ]);
            }
        }

        $totalIncome = $income->sum('amount');
        $totalExpense = $expense->sum('amount');
        $balance = $openingBalance + $totalIncome - $totalExpense;

        return response()->json([
            'success' => true,
            'opening_balance' => $openingBalance,
            'income' => $income,
            'expense' => $expense,
            'total_income' => $totalIncome,
            'total_expense' => $totalExpense,
            'balance' => $balance,
        ]);
    }
} 