<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLoyaltyCardsTable extends Migration
{
    public function up()
    {
        Schema::create('loyalty_cards', function (Blueprint $table) {
            $table->id();
            $table->string('card_name');
            $table->string('calculation_type'); // Point-wise or Discount-wise
            $table->string('point_calculation_mode')->nullable(); // Range-wise or Threshold-wise
            $table->integer('points_per_threshold')->nullable(); // Threshold amount (e.g., 1000)
            $table->integer('points_per_threshold_value')->nullable(); // Points per threshold (e.g., 1)
            $table->string('threshold_method')->nullable(); // per-threshold or single-threshold
            $table->decimal('single_threshold_amount', 10, 2)->nullable();
            $table->integer('single_threshold_points')->nullable();
            $table->decimal('single_threshold_percentage', 5, 2)->nullable();
            $table->string('single_threshold_operator', 2)->nullable(); // > or >=
            $table->integer('percentage_per_threshold')->nullable();
            $table->decimal('percentage_per_threshold_value', 5, 2)->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('loyalty_cards');
    }
}