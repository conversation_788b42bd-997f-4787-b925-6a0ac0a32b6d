<?php

declare(strict_types=1);

namespace ParaTest\Coverage;

use <PERSON><PERSON><PERSON><PERSON>n\CodeCoverage\CodeCoverage;
use SplFileInfo;

use function assert;

/** @internal */
final readonly class CoverageMerger
{
    public function __construct(
        private CodeCoverage $coverage
    ) {
    }

    public function addCoverageFromFile(SplFileInfo $coverageFile): void
    {
        if (! $coverageFile->isFile() || $coverageFile->getSize() === 0) {
            return;
        }

        /** @psalm-suppress UnresolvableInclude **/
        $coverage = include $coverageFile->getPathname();
        assert($coverage instanceof CodeCoverage);

        $this->coverage->merge($coverage);
    }
}
