<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StaffLedger extends Model
{
    use HasFactory;

    protected $table = 'staff_ledger';

    protected $fillable = [
        'staff_id',
        'name',
        'address',
        'mobile_no',
        'whatsapp_no',
        'telephone_no',
        'nic',
        'opening_balance',
        'account_group',
        'email',
        'position',
        'join_date',
        'date',
        'profile_picture',
    ];

    protected $casts = [
        'opening_balance' => 'decimal:2',
        'join_date' => 'date',
        'date' => 'date',
    ];
}