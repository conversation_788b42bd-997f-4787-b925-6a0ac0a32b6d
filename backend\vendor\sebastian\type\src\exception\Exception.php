<?php declare(strict_types=1);
/*
 * This file is part of sebastian/type.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace Sebastian<PERSON>\Type;

use Throwable;

/**
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for this library
 */
interface Exception extends Throwable
{
}
