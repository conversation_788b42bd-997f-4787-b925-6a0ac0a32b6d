<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stock_rechecks', function (Blueprint $table) {
            // Add batch-level support
            $table->string('batch_number')->nullable()->after('location');
            $table->unsignedBigInteger('product_variant_id')->nullable()->after('batch_number');
            $table->date('expiry_date')->nullable()->after('product_variant_id');
            $table->enum('recheck_type', ['product', 'batch'])->default('product')->after('expiry_date');
            
            // Add foreign key for product variant
            $table->foreign('product_variant_id')->references('product_variant_id')->on('product_variants')->onDelete('cascade');
            
            // Add index for batch lookups
            $table->index(['item_code', 'batch_number']);
            $table->index(['product_variant_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stock_rechecks', function (Blueprint $table) {
            $table->dropForeign(['product_variant_id']);
            $table->dropIndex(['item_code', 'batch_number']);
            $table->dropIndex(['product_variant_id']);
            $table->dropColumn(['batch_number', 'product_variant_id', 'expiry_date', 'recheck_type']);
        });
    }
};
