{"name": "sebastian/complexity", "description": "Library for calculating the complexity of PHP code units", "type": "library", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy"}, "prefer-stable": true, "require": {"php": ">=8.2", "nikic/php-parser": "^5.0"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "config": {"platform": {"php": "8.2.0"}, "optimize-autoloader": true, "sort-packages": true}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-main": "4.0-dev"}}}