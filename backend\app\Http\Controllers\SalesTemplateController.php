<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\SalesTemplate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SalesTemplateController extends Controller
{
    /**
     * Retrieve all sales templates.
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $templates = SalesTemplate::all();
            return response()->json([
                'success' => true,
                'data' => $templates,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve templates: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a new sales template.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:sales_templates,name',
            'content' => 'required|string',
            'is_default' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            if ($request->is_default) {
                SalesTemplate::where('is_default', true)->update(['is_default' => false]);
            }

            $template = SalesTemplate::create([
                'name' => $request->name,
                'content' => $request->content,
                'is_default' => $request->is_default ?? false,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Template created successfully',
                'data' => $template,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create template: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update an existing sales template.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:sales_templates,name,' . $id,
            'content' => 'required|string',
            'is_default' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $template = SalesTemplate::findOrFail($id);
            if ($request->is_default) {
                SalesTemplate::where('is_default', true)->update(['is_default' => false]);
            }

            $template->update([
                'name' => $request->name,
                'content' => $request->content,
                'is_default' => $request->is_default ?? $template->is_default,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Template updated successfully',
                'data' => $template,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update template: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a sales template.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        try {
            $template = SalesTemplate::findOrFail($id);
            $template->delete();

            return response()->json([
                'success' => true,
                'message' => 'Template deleted successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete template: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Set a template as default.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function setDefault($id): JsonResponse
    {
        try {
            SalesTemplate::where('is_default', true)->update(['is_default' => false]);
            $template = SalesTemplate::findOrFail($id);
            $template->update(['is_default' => true]);

            return response()->json([
                'success' => true,
                'message' => 'Default template set successfully',
                'data' => $template,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to set default template: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send a message to a customer.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendMessage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // In a real application, integrate with an SMS gateway (e.g., Twilio)
            // For demonstration, log the message and simulate success
            \Log::info('Sending message', [
                'phone' => $request->phone,
                'message' => $request->message,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message: ' . $e->getMessage(),
            ], 500);
        }
    }
}
