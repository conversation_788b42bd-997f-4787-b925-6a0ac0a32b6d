<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('quotations', function (Blueprint $table) {
            $table->id();
            $table->string('quotation_no')->unique();
            $table->date('date');
            $table->time('time');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->string('agent_name')->nullable();
            $table->string('agent_phone')->nullable();
            $table->decimal('total', 10, 2);
            $table->string('approved_by')->nullable();
            $table->string('next_approval_to')->nullable();
            $table->text('details')->nullable();
            $table->string('date_time_approval')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('quotations');
    }
};