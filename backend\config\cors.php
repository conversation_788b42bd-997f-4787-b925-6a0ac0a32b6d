<?php

return [

    /*
    |--------------------------------------------------------------------------
    | <PERSON><PERSON> CORS Options
    |--------------------------------------------------------------------------
    |
    | The allowed methods, origins, headers, and other settings for handling
    | Cross-Origin Resource Sharing (CORS) requests.
    |
    */

    'paths' => ['api/*', 'sanctum/csrf-cookie'],

    'allowed_methods' => ['*'],

    'allowed_origins' => ['*'],

    'allowed_origins_patterns' => [],

    'allowed_headers' => ['*'],

    'exposed_headers' => [],

    'max_age' => 0,

    'supports_credentials' => false,

];
