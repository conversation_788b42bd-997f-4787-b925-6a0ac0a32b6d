<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('staff_ledger', function (Blueprint $table) {
            $table->id();
            $table->string('staff_id')->unique()->nullable();
            $table->string('name');
            $table->text('address')->nullable();
            $table->string('mobile_no')->nullable();
            $table->string('whatsapp_no')->nullable();
            $table->string('telephone_no')->nullable();
            $table->string('nic')->nullable();
            $table->decimal('opening_balance', 15, 2)->default(0.0);
            $table->string('account_group');
            $table->string('email')->nullable();
            $table->string('position')->nullable();
            $table->date('join_date')->nullable();
            $table->date('date')->nullable();
            $table->string('profile_picture')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('staff_ledger');
    }
};
