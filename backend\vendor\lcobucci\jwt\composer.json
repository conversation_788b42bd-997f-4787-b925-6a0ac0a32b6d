{"name": "lcobucci/jwt", "description": "A simple library to work with JSON Web Token and JSON Web Signature", "type": "library", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "keywords": ["JWT", "JWS"], "license": ["BSD-3-<PERSON><PERSON>"], "require": {"php": "^7.4 || ^8.0", "ext-mbstring": "*", "ext-openssl": "*", "lcobucci/clock": "^2.0"}, "require-dev": {"infection/infection": "^0.20", "lcobucci/coding-standard": "^6.0", "mikey179/vfsstream": "^1.6", "phpbench/phpbench": "^0.17", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/php-invoker": "^3.1", "phpunit/phpunit": "^9.4"}, "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "autoload-dev": {"psr-4": {"Lcobucci\\JWT\\": ["test/_keys", "test/unit", "test/performance"], "Lcobucci\\JWT\\FunctionalTests\\": "test/functional"}}, "config": {"preferred-install": "dist", "sort-packages": true}, "extra": {"branch-alias": {"dev-master": "4.0-dev"}}}