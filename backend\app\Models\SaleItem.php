<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SaleItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'sale_id', 'product_id', 'product_variant_id', 'batch_number', 'expiry_date', 'product_name', 'quantity', 'mrp', 'unit_price', 'discount', 'total','special_discount','supplier',
        'category',
        'store_location',
        'deleted_by',
        'free_qty',
    ];

    public function sale()
    {
        // return $this->belongsTo(Sale::class);
        return $this->belongsTo(Sale::class, 'sale_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'product_id'); // Corrected foreign key to product_id
    }

    public function productVariant()
    {
        return $this->belongsTo(ProductVariant::class, 'product_variant_id', 'product_variant_id');
    }
}