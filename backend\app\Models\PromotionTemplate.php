<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PromotionTemplate extends Model
{
     use HasFactory;

    protected $fillable = ['name', 'customer_details', 'excel_details', 'message'];

    protected $casts = [
        'customer_details' => 'array',
        'excel_details' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
