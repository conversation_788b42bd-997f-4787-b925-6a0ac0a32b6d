{"name": "laravel/fortify", "description": "Backend controllers and scaffolding for Laravel authentication.", "keywords": ["laravel", "auth"], "license": "MIT", "support": {"issues": "https://github.com/laravel/fortify/issues", "source": "https://github.com/laravel/fortify"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-json": "*", "bacon/bacon-qr-code": "^3.0", "illuminate/support": "^10.0|^11.0|^12.0", "symfony/console": "^6.0|^7.0", "pragmarx/google2fa": "^8.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^8.16|^9.0|^10.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.4|^11.3"}, "autoload": {"psr-4": {"Laravel\\Fortify\\": "src/"}}, "autoload-dev": {"psr-4": {"Laravel\\Fortify\\Tests\\": "tests/", "App\\": "workbench/app/", "Database\\Factories\\": "workbench/database/factories/"}, "classmap": ["stubs/"]}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}, "laravel": {"providers": ["Laravel\\Fortify\\FortifyServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": "@prepare", "prepare": "@php vendor/bin/testbench package:discover --ansi", "lint": "@php vendor/bin/phpstan analyse", "test": "@php vendor/bin/phpunit"}}