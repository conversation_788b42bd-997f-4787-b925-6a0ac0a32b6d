# PsySH

PsySH is a runtime developer console, interactive debugger and [REPL](https://en.wikipedia.org/wiki/Read%E2%80%93eval%E2%80%93print_loop) for PHP. Learn more at [psysh.org](http://psysh.org/) and [in the manual](https://github.com/bobthecow/psysh/wiki/Home).


[![Package version](https://img.shields.io/packagist/v/psy/psysh.svg?style=flat-square)](https://packagist.org/packages/psy/psysh)
[![Monthly downloads](http://img.shields.io/packagist/dm/psy/psysh.svg?style=flat-square)](https://packagist.org/packages/psy/psysh)
[![Made out of awesome](https://img.shields.io/badge/made_out_of_awesome-✓-brightgreen.svg?style=flat-square)](http://psysh.org)

[![Build status](https://img.shields.io/github/actions/workflow/status/bobthecow/psysh/tests.yml?branch=main&style=flat-square)](https://github.com/bobthecow/psysh/actions?query=branch:main)
[![StyleCI](https://styleci.io/repos/4549925/shield)](https://styleci.io/repos/4549925)


<a id="downloading-the-manual"></a>

## [PsySH manual](https://github.com/bobthecow/psysh/wiki/Home)

### [💾 Installation](https://github.com/bobthecow/psysh/wiki/Installation)
 * [📕 PHP manual installation](https://github.com/bobthecow/psysh/wiki/PHP-manual)
 * <a class="internal present" href="https://github.com/bobthecow/psysh/wiki/Windows"><img src="https://user-images.githubusercontent.com/53660/40878809-407e8368-664b-11e8-8455-f11602c41dfe.png" width="18"> Windows</a>

### [🖥 Usage](https://github.com/bobthecow/psysh/wiki/Usage)
 * [✨ Magic variables](https://github.com/bobthecow/psysh/wiki/Magic-variables)
 * [⏳ Managing history](https://github.com/bobthecow/psysh/wiki/History)
 * [💲 System shell integration](https://github.com/bobthecow/psysh/wiki/Shell-integration)
 * [🎥 Tutorials & guides](https://github.com/bobthecow/psysh/wiki/Tutorials)
 * [🐛 Troubleshooting](https://github.com/bobthecow/psysh/wiki/Troubleshooting)

### [📢 Commands](https://github.com/bobthecow/psysh/wiki/Commands)

### [🛠 Configuration](https://github.com/bobthecow/psysh/wiki/Configuration)
 * [🎛 Config options](https://github.com/bobthecow/psysh/wiki/Config-options)
 * [🎨 Themes](https://github.com/bobthecow/psysh/wiki/Themes)
 * [📄 Sample config file](https://github.com/bobthecow/psysh/wiki/Sample-config)

### [🔌 Integrations](https://github.com/bobthecow/psysh/wiki/Integrations)
