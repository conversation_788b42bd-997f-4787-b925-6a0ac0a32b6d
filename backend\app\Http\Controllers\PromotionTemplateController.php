<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PromotionTemplate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class PromotionTemplateController extends Controller
{
    public function index(): JsonResponse
    {
        try {
            $templates = PromotionTemplate::all();
            return response()->json([
                'success' => true,
                'data' => $templates,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve templates: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Retrieve a specific promotion template by ID.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        try {
            $template = PromotionTemplate::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $template,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Template not found: ' . $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Save a new promotion template.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:promotion_templates,name',
            'customer_details' => 'nullable|array',
            'excel_details' => 'nullable|array',
            'message' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $template = PromotionTemplate::create([
                'name' => $request->name,
                'customer_details' => $request->customer_details,
                'excel_details' => $request->excel_details,
                'message' => $request->message,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Template saved successfully',
                'data' => $template,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save template: ' . $e->getMessage(),
            ], 500);
        }
    }
}
