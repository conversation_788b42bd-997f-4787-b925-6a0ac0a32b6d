<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class MultipleUsersSeeder extends Seeder
{
    public function run()
    {
        $users = [
            [
                'email' => '<EMAIL>',
                'name' => 'Ilanggo',
                'password' => 'ilango@123',
                'role' => 'cashier',
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'Manager',
                'password' => 'password123',
                'role' => 'manager',
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'Dinojini',
                'password' => 'dinojini@123',
                'role' => 'cashier',
            ],
        ];

        foreach ($users as $userData) {
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                [
                    'name' => $userData['name'],
                    'username' => strtolower(explode('@', $userData['email'])[0]),
                    'password' => Hash::make($userData['password']),
                    'role' => $userData['role'],
                    'status' => 'active',
                    'photo' => null,
                    'email_verified_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                    'is_active' => true,
                ]
            );

            $role = \Spatie\Permission\Models\Role::firstOrCreate(['name' => $userData['role'], 'guard_name' => 'api']);
            DB::table('model_has_roles')->updateOrInsert(
                [
                    'model_id' => $user->id,
                    'model_type' => get_class($user),
                    'role_id' => $role->id
                ],
                [
                    'model_id' => $user->id,
                    'model_type' => get_class($user),
                    'role_id' => $role->id
                ]
            );
            Log::info('User seeded with role', ['email' => $user->email, 'role' => $userData['role']]);
        }
    }
}