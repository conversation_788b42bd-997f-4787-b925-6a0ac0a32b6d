<?php

namespace App\Http\Controllers;

use App\Models\UserLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class UserActivityController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        try {
            $query = UserLog::with('user:id,name,email,username');

            // Apply filters
            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->whereBetween('created_at', [
                    $request->start_date . ' 00:00:00',
                    $request->end_date . ' 23:59:59'
                ]);
            }

            if ($request->filled('action')) {
                $query->where('action', 'like', '%' . $request->action . '%');
            }

            if ($request->filled('module')) {
                $query->where('module', $request->module);
            }

            if ($request->filled('search')) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    $q->whereHas('user', function ($userQuery) use ($searchTerm) {
                        $userQuery->where('name', 'like', '%' . $searchTerm . '%')
                                 ->orWhere('email', 'like', '%' . $searchTerm . '%')
                                 ->orWhere('username', 'like', '%' . $searchTerm . '%');
                    })
                    ->orWhere('action', 'like', '%' . $searchTerm . '%')
                    ->orWhere('ip_address', 'like', '%' . $searchTerm . '%')
                    ->orWhere('module', 'like', '%' . $searchTerm . '%');
                });
            }

            // Get paginated results
            $perPage = $request->get('per_page', 20);
            $activities = $query->orderBy('created_at', 'desc')
                               ->paginate($perPage);

            // Transform data for frontend
            $transformedData = $activities->getCollection()->map(function ($log) {
                return [
                    'id' => $log->id,
                    'user_id' => $log->user_id,
                    'user_name' => $log->user->name ?? 'Unknown User',
                    'user_email' => $log->user->email ?? 'No email',
                    'user_username' => $log->user->username ?? 'No username',
                    'action' => $log->action,
                    'formatted_action' => $log->formatted_action,
                    'action_color' => $log->action_color,
                    'ip_address' => $log->ip_address,
                    'user_agent' => $log->user_agent,
                    'module' => $log->module,
                    'record_id' => $log->record_id,
                    'details' => $log->details,
                    'old_values' => $log->old_values,
                    'new_values' => $log->new_values,
                    'created_at' => $log->created_at,
                    'updated_at' => $log->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedData,
                'pagination' => [
                    'current_page' => $activities->currentPage(),
                    'last_page' => $activities->lastPage(),
                    'per_page' => $activities->perPage(),
                    'total' => $activities->total(),
                    'from' => $activities->firstItem(),
                    'to' => $activities->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch activity logs',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function summary(Request $request): JsonResponse
    {
        try {
            $query = UserLog::query();

            // Apply date range if provided
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->whereBetween('created_at', [
                    $request->start_date . ' 00:00:00',
                    $request->end_date . ' 23:59:59'
                ]);
            }

            // Get summary statistics
            $summary = [
                'total_activities' => $query->count(),
                'unique_users' => $query->distinct('user_id')->count('user_id'),
                'unique_ips' => $query->distinct('ip_address')->count('ip_address'),
                'today_activities' => UserLog::whereDate('created_at', today())->count(),
                'action_breakdown' => $query->select('action', DB::raw('count(*) as count'))
                                          ->groupBy('action')
                                          ->orderBy('count', 'desc')
                                          ->limit(10)
                                          ->get(),
                'module_breakdown' => $query->select('module', DB::raw('count(*) as count'))
                                          ->whereNotNull('module')
                                          ->groupBy('module')
                                          ->orderBy('count', 'desc')
                                          ->limit(10)
                                          ->get(),
                'recent_activities' => $query->with('user:id,name,email')
                                           ->orderBy('created_at', 'desc')
                                           ->limit(5)
                                           ->get()
                                           ->map(function ($log) {
                                               return [
                                                   'user_name' => $log->user->name ?? 'Unknown',
                                                   'action' => $log->action,
                                                   'created_at' => $log->created_at
                                               ];
                                           })
            ];

            return response()->json([
                'success' => true,
                'data' => $summary
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch summary',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function userActivity($userId, Request $request): JsonResponse
    {
        try {
            $query = UserLog::where('user_id', $userId)
                           ->with('user:id,name,email,username');

            // Apply date range if provided
            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->whereBetween('created_at', [
                    $request->start_date . ' 00:00:00',
                    $request->end_date . ' 23:59:59'
                ]);
            }

            $activities = $query->orderBy('created_at', 'desc')
                               ->paginate($request->get('per_page', 20));

            $transformedData = $activities->getCollection()->map(function ($log) {
                return [
                    'id' => $log->id,
                    'action' => $log->action,
                    'formatted_action' => $log->formatted_action,
                    'action_color' => $log->action_color,
                    'ip_address' => $log->ip_address,
                    'module' => $log->module,
                    'details' => $log->details,
                    'created_at' => $log->created_at,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $transformedData,
                'pagination' => [
                    'current_page' => $activities->currentPage(),
                    'last_page' => $activities->lastPage(),
                    'per_page' => $activities->perPage(),
                    'total' => $activities->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch user activity',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // Helper method to log user activity
    public static function logActivity($userId, $action, $module = null, $recordId = null, $details = null, $oldValues = null, $newValues = null)
    {
        try {
            // Get request data safely
            $request = request();
            $ipAddress = $request ? $request->ip() : '127.0.0.1';
            $userAgent = $request ? $request->userAgent() : 'Unknown';
            
            \Log::info('UserActivityController::logActivity called', [
                'user_id' => $userId,
                'action' => $action,
                'module' => $module,
                'record_id' => $recordId,
                'ip_address' => $ipAddress
            ]);
            
            UserLog::create([
                'user_id' => $userId,
                'action' => $action,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'module' => $module,
                'record_id' => $recordId,
                'details' => $details,
                'old_values' => $oldValues,
                'new_values' => $newValues,
            ]);
            
            \Log::info('User activity logged successfully', [
                'user_id' => $userId,
                'action' => $action
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to log user activity: ' . $e->getMessage(), [
                'user_id' => $userId,
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }


} 