<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductVariantsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id('product_variant_id');
            $table->unsignedBigInteger('product_id');
            $table->string('batch_number')->nullable();
            $table->date('expiry_date')->nullable();
            $table->decimal('buying_cost', 15, 2)->nullable();
            $table->decimal('sales_price', 15, 2)->nullable();
            $table->decimal('minimum_price', 15, 2)->nullable();
            $table->decimal('wholesale_price', 15, 2)->nullable();
            $table->string('barcode')->nullable();
            $table->decimal('mrp', 15, 2)->nullable();
            $table->decimal('minimum_stock_quantity', 15, 2)->nullable();
            $table->decimal('opening_stock_quantity', 15, 2)->nullable();
            $table->decimal('opening_stock_value', 15, 2)->nullable();
            $table->string('store_location')->nullable();
            $table->string('cabinet')->nullable();
            $table->string('row')->nullable();
            $table->json('extra_fields')->nullable();
            $table->timestamps();

            $table->foreign('product_id')->references('product_id')->on('products')->onDelete('cascade');
            $table->unique(['product_id', 'batch_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
}
