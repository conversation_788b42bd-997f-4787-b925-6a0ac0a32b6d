{"name": "laravel/jetstream", "description": "Tailwind scaffolding for the Laravel framework.", "keywords": ["laravel", "tailwind", "auth"], "license": "MIT", "support": {"issues": "https://github.com/laravel/jetstream/issues", "source": "https://github.com/laravel/jetstream"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2.0", "ext-json": "*", "illuminate/console": "^11.0|^12.0", "illuminate/support": "^11.0|^12.0", "laravel/fortify": "^1.20", "mobiledetect/mobiledetectlib": "^4.8.08", "symfony/console": "^7.0"}, "require-dev": {"inertiajs/inertia-laravel": "^2.0", "laravel/sanctum": "^4.0", "livewire/livewire": "^3.3", "mockery/mockery": "^1.0", "orchestra/testbench": "^9.0|^10.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^11.0"}, "autoload": {"psr-4": {"Laravel\\Jetstream\\": "src/"}}, "autoload-dev": {"psr-4": {"Laravel\\Jetstream\\Tests\\": "tests/", "App\\": "stubs/app/", "Database\\Factories\\": "database/factories/"}}, "extra": {"laravel": {"providers": ["Laravel\\Jetstream\\JetstreamServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}