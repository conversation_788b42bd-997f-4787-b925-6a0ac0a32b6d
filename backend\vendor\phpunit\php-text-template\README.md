[![Latest Stable Version](https://poser.pugx.org/phpunit/php-text-template/v/stable.png)](https://packagist.org/packages/phpunit/php-text-template)
[![CI Status](https://github.com/sebastian<PERSON>mann/php-text-template/workflows/CI/badge.svg)](https://github.com/sebastian<PERSON>mann/php-text-template/actions)
[![codecov](https://codecov.io/gh/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/branch/main/graph/badge.svg)](https://codecov.io/gh/sebas<PERSON><PERSON><PERSON>/php-text-template)

# php-text-template

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

    composer require phpunit/php-text-template

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

    composer require --dev phpunit/php-text-template

