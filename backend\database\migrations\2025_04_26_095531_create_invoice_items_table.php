<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Invoice; // Import the Invoice model
use App\Models\Product;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            // Use constrained foreignId for better relationship handling
            $table->foreignIdFor(Invoice::class)->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products', 'product_id')->onDelete('cascade');
            $table->string('description')->nullable();
            $table->decimal('quantity', 15, 2); // Use decimal for potential fractional quantities
            $table->decimal('unit_price', 15, 2);
            $table->decimal('discount_amount', 15, 2)->default(0);
            $table->decimal('special_discount', 15, 2)->default(0);
            $table->decimal('discount_percentage', 5, 2)->default(0); // e.g., 10.50 for 10.50%
            $table->decimal('total', 15, 2); // Calculated item total: (qty * unit_price) - discount_amount
            $table->decimal('total_buying_cost', 15, 2)->default(0);
            $table->string('supplier')->nullable();
            $table->string('category')->nullable();
            $table->string('store_location')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_items');
    }
};