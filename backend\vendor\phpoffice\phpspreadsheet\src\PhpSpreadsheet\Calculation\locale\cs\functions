############################################################
##
## PhpSpreadsheet - function name translations
##
## Ce<PERSON><PERSON> (Czech)
##
############################################################


##
## Funkce pro práci s datový<PERSON> krychlemi (Cube Functions)
##
CUBEKPIMEMBER = CUBEKPIMEMBER
CUBEMEMBER = CUBEMEMBER
CUBEMEMBERPROPERTY = CUBEMEMBERPROPERTY
CUBERANKEDMEMBER = CUBERANKEDMEMBER
CUBESET = CUBESET
CUBESETCOUNT = CUBESETCOUNT
CUBEVALUE = CUBEVALUE

##
## Funkce databáze (Database Functions)
##
DAVERAGE = DPRŮMĚR
DCOUNT = DPOČET
DCOUNTA = DPOČET2
DGET = DZÍSKAT
DMAX = DMAX
DMIN = DMIN
DPRODUCT = DSOUČIN
DSTDEV = DSMODCH.VÝBĚR
DSTDEVP = DSMODCH
DSUM = DSUMA
DVAR = DVAR.VÝBĚR
DVARP = DVAR

##
## Funkce data a času (Date & Time Functions)
##
DATE = DATUM
DATEVALUE = DATUMHODN
DAY = DEN
DAYS = DAYS
DAYS360 = ROK360
EDATE = EDATE
EOMONTH = EOMONTH
HOUR = HODINA
ISOWEEKNUM = ISOWEEKNUM
MINUTE = MINUTA
MONTH = MĚSÍC
NETWORKDAYS = NETWORKDAYS
NETWORKDAYS.INTL = NETWORKDAYS.INTL
NOW = NYNÍ
SECOND = SEKUNDA
TIME = ČAS
TIMEVALUE = ČASHODN
TODAY = DNES
WEEKDAY = DENTÝDNE
WEEKNUM = WEEKNUM
WORKDAY = WORKDAY
WORKDAY.INTL = WORKDAY.INTL
YEAR = ROK
YEARFRAC = YEARFRAC

##
## Inženýrské funkce (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BIN2DEC
BIN2HEX = BIN2HEX
BIN2OCT = BIN2OCT
BITAND = BITAND
BITLSHIFT = BITLSHIFT
BITOR = BITOR
BITRSHIFT = BITRSHIFT
BITXOR = BITXOR
COMPLEX = COMPLEX
CONVERT = CONVERT
DEC2BIN = DEC2BIN
DEC2HEX = DEC2HEX
DEC2OCT = DEC2OCT
DELTA = DELTA
ERF = ERF
ERF.PRECISE = ERF.PRECISE
ERFC = ERFC
ERFC.PRECISE = ERFC.PRECISE
GESTEP = GESTEP
HEX2BIN = HEX2BIN
HEX2DEC = HEX2DEC
HEX2OCT = HEX2OCT
IMABS = IMABS
IMAGINARY = IMAGINARY
IMARGUMENT = IMARGUMENT
IMCONJUGATE = IMCONJUGATE
IMCOS = IMCOS
IMCOSH = IMCOSH
IMCOT = IMCOT
IMCSC = IMCSC
IMCSCH = IMCSCH
IMDIV = IMDIV
IMEXP = IMEXP
IMLN = IMLN
IMLOG10 = IMLOG10
IMLOG2 = IMLOG2
IMPOWER = IMPOWER
IMPRODUCT = IMPRODUCT
IMREAL = IMREAL
IMSEC = IMSEC
IMSECH = IMSECH
IMSIN = IMSIN
IMSINH = IMSINH
IMSQRT = IMSQRT
IMSUB = IMSUB
IMSUM = IMSUM
IMTAN = IMTAN
OCT2BIN = OCT2BIN
OCT2DEC = OCT2DEC
OCT2HEX = OCT2HEX

##
## Finanční funkce (Financial Functions)
##
ACCRINT = ACCRINT
ACCRINTM = ACCRINTM
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = COUPDAYBS
COUPDAYS = COUPDAYS
COUPDAYSNC = COUPDAYSNC
COUPNCD = COUPNCD
COUPNUM = COUPNUM
COUPPCD = COUPPCD
CUMIPMT = CUMIPMT
CUMPRINC = CUMPRINC
DB = ODPIS.ZRYCH
DDB = ODPIS.ZRYCH2
DISC = DISC
DOLLARDE = DOLLARDE
DOLLARFR = DOLLARFR
DURATION = DURATION
EFFECT = EFFECT
FV = BUDHODNOTA
FVSCHEDULE = FVSCHEDULE
INTRATE = INTRATE
IPMT = PLATBA.ÚROK
IRR = MÍRA.VÝNOSNOSTI
ISPMT = ISPMT
MDURATION = MDURATION
MIRR = MOD.MÍRA.VÝNOSNOSTI
NOMINAL = NOMINAL
NPER = POČET.OBDOBÍ
NPV = ČISTÁ.SOUČHODNOTA
ODDFPRICE = ODDFPRICE
ODDFYIELD = ODDFYIELD
ODDLPRICE = ODDLPRICE
ODDLYIELD = ODDLYIELD
PDURATION = PDURATION
PMT = PLATBA
PPMT = PLATBA.ZÁKLAD
PRICE = PRICE
PRICEDISC = PRICEDISC
PRICEMAT = PRICEMAT
PV = SOUČHODNOTA
RATE = ÚROKOVÁ.MÍRA
RECEIVED = RECEIVED
RRI = RRI
SLN = ODPIS.LIN
SYD = ODPIS.NELIN
TBILLEQ = TBILLEQ
TBILLPRICE = TBILLPRICE
TBILLYIELD = TBILLYIELD
VDB = ODPIS.ZA.INT
XIRR = XIRR
XNPV = XNPV
YIELD = YIELD
YIELDDISC = YIELDDISC
YIELDMAT = YIELDMAT

##
## Informační funkce (Information Functions)
##
CELL = POLÍČKO
ERROR.TYPE = CHYBA.TYP
INFO = O.PROSTŘEDÍ
ISBLANK = JE.PRÁZDNÉ
ISERR = JE.CHYBA
ISERROR = JE.CHYBHODN
ISEVEN = ISEVEN
ISFORMULA = ISFORMULA
ISLOGICAL = JE.LOGHODN
ISNA = JE.NEDEF
ISNONTEXT = JE.NETEXT
ISNUMBER = JE.ČISLO
ISODD = ISODD
ISREF = JE.ODKAZ
ISTEXT = JE.TEXT
N = N
NA = NEDEF
SHEET = SHEET
SHEETS = SHEETS
TYPE = TYP

##
## Logické funkce (Logical Functions)
##
AND = A
FALSE = NEPRAVDA
IF = KDYŽ
IFERROR = IFERROR
IFNA = IFNA
IFS = IFS
NOT = NE
OR = NEBO
SWITCH = SWITCH
TRUE = PRAVDA
XOR = XOR

##
## Vyhledávací funkce a funkce pro odkazy (Lookup & Reference Functions)
##
ADDRESS = ODKAZ
AREAS = POČET.BLOKŮ
CHOOSE = ZVOLIT
COLUMN = SLOUPEC
COLUMNS = SLOUPCE
FORMULATEXT = FORMULATEXT
GETPIVOTDATA = ZÍSKATKONTDATA
HLOOKUP = VVYHLEDAT
HYPERLINK = HYPERTEXTOVÝ.ODKAZ
INDEX = INDEX
INDIRECT = NEPŘÍMÝ.ODKAZ
LOOKUP = VYHLEDAT
MATCH = POZVYHLEDAT
OFFSET = POSUN
ROW = ŘÁDEK
ROWS = ŘÁDKY
RTD = RTD
TRANSPOSE = TRANSPOZICE
VLOOKUP = SVYHLEDAT

##
## Matematické a trigonometrické funkce (Math & Trig Functions)
##
ABS = ABS
ACOS = ARCCOS
ACOSH = ARCCOSH
ACOT = ACOT
ACOTH = ACOTH
AGGREGATE = AGGREGATE
ARABIC = ARABIC
ASIN = ARCSIN
ASINH = ARCSINH
ATAN = ARCTG
ATAN2 = ARCTG2
ATANH = ARCTGH
BASE = BASE
CEILING.MATH = CEILING.MATH
COMBIN = KOMBINACE
COMBINA = COMBINA
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = DECIMAL
DEGREES = DEGREES
EVEN = ZAOKROUHLIT.NA.SUDÉ
EXP = EXP
FACT = FAKTORIÁL
FACTDOUBLE = FACTDOUBLE
FLOOR.MATH = FLOOR.MATH
GCD = GCD
INT = CELÁ.ČÁST
LCM = LCM
LN = LN
LOG = LOGZ
LOG10 = LOG
MDETERM = DETERMINANT
MINVERSE = INVERZE
MMULT = SOUČIN.MATIC
MOD = MOD
MROUND = MROUND
MULTINOMIAL = MULTINOMIAL
MUNIT = MUNIT
ODD = ZAOKROUHLIT.NA.LICHÉ
PI = PI
POWER = POWER
PRODUCT = SOUČIN
QUOTIENT = QUOTIENT
RADIANS = RADIANS
RAND = NÁHČÍSLO
RANDBETWEEN = RANDBETWEEN
ROMAN = ROMAN
ROUND = ZAOKROUHLIT
ROUNDDOWN = ROUNDDOWN
ROUNDUP = ROUNDUP
SEC = SEC
SECH = SECH
SERIESSUM = SERIESSUM
SIGN = SIGN
SIN = SIN
SINH = SINH
SQRT = ODMOCNINA
SQRTPI = SQRTPI
SUBTOTAL = SUBTOTAL
SUM = SUMA
SUMIF = SUMIF
SUMIFS = SUMIFS
SUMPRODUCT = SOUČIN.SKALÁRNÍ
SUMSQ = SUMA.ČTVERCŮ
SUMX2MY2 = SUMX2MY2
SUMX2PY2 = SUMX2PY2
SUMXMY2 = SUMXMY2
TAN = TG
TANH = TGH
TRUNC = USEKNOUT

##
## Statistické funkce (Statistical Functions)
##
AVEDEV = PRŮMODCHYLKA
AVERAGE = PRŮMĚR
AVERAGEA = AVERAGEA
AVERAGEIF = AVERAGEIF
AVERAGEIFS = AVERAGEIFS
BETA.DIST = BETA.DIST
BETA.INV = BETA.INV
BINOM.DIST = BINOM.DIST
BINOM.DIST.RANGE = BINOM.DIST.RANGE
BINOM.INV = BINOM.INV
CHISQ.DIST = CHISQ.DIST
CHISQ.DIST.RT = CHISQ.DIST.RT
CHISQ.INV = CHISQ.INV
CHISQ.INV.RT = CHISQ.INV.RT
CHISQ.TEST = CHISQ.TEST
CONFIDENCE.NORM = CONFIDENCE.NORM
CONFIDENCE.T = CONFIDENCE.T
CORREL = CORREL
COUNT = POČET
COUNTA = POČET2
COUNTBLANK = COUNTBLANK
COUNTIF = COUNTIF
COUNTIFS = COUNTIFS
COVARIANCE.P = COVARIANCE.P
COVARIANCE.S = COVARIANCE.S
DEVSQ = DEVSQ
EXPON.DIST = EXPON.DIST
F.DIST = F.DIST
F.DIST.RT = F.DIST.RT
F.INV = F.INV
F.INV.RT = F.INV.RT
F.TEST = F.TEST
FISHER = FISHER
FISHERINV = FISHERINV
FORECAST.ETS = FORECAST.ETS
FORECAST.ETS.CONFINT = FORECAST.ETS.CONFINT
FORECAST.ETS.SEASONALITY = FORECAST.ETS.SEASONALITY
FORECAST.ETS.STAT = FORECAST.ETS.STAT
FORECAST.LINEAR = FORECAST.LINEAR
FREQUENCY = ČETNOSTI
GAMMA = GAMMA
GAMMA.DIST = GAMMA.DIST
GAMMA.INV = GAMMA.INV
GAMMALN = GAMMALN
GAMMALN.PRECISE = GAMMALN.PRECISE
GAUSS = GAUSS
GEOMEAN = GEOMEAN
GROWTH = LOGLINTREND
HARMEAN = HARMEAN
HYPGEOM.DIST = HYPGEOM.DIST
INTERCEPT = INTERCEPT
KURT = KURT
LARGE = LARGE
LINEST = LINREGRESE
LOGEST = LOGLINREGRESE
LOGNORM.DIST = LOGNORM.DIST
LOGNORM.INV = LOGNORM.INV
MAX = MAX
MAXA = MAXA
MAXIFS = MAXIFS
MEDIAN = MEDIAN
MIN = MIN
MINA = MINA
MINIFS = MINIFS
MODE.MULT = MODE.MULT
MODE.SNGL = MODE.SNGL
NEGBINOM.DIST = NEGBINOM.DIST
NORM.DIST = NORM.DIST
NORM.INV = NORM.INV
NORM.S.DIST = NORM.S.DIST
NORM.S.INV = NORM.S.INV
PEARSON = PEARSON
PERCENTILE.EXC = PERCENTIL.EXC
PERCENTILE.INC = PERCENTIL.INC
PERCENTRANK.EXC = PERCENTRANK.EXC
PERCENTRANK.INC = PERCENTRANK.INC
PERMUT = PERMUTACE
PERMUTATIONA = PERMUTATIONA
PHI = PHI
POISSON.DIST = POISSON.DIST
PROB = PROB
QUARTILE.EXC = QUARTIL.EXC
QUARTILE.INC = QUARTIL.INC
RANK.AVG = RANK.AVG
RANK.EQ = RANK.EQ
RSQ = RKQ
SKEW = SKEW
SKEW.P = SKEW.P
SLOPE = SLOPE
SMALL = SMALL
STANDARDIZE = STANDARDIZE
STDEV.P = SMODCH.P
STDEV.S = SMODCH.VÝBĚR.S
STDEVA = STDEVA
STDEVPA = STDEVPA
STEYX = STEYX
T.DIST = T.DIST
T.DIST.2T = T.DIST.2T
T.DIST.RT = T.DIST.RT
T.INV = T.INV
T.INV.2T = T.INV.2T
T.TEST = T.TEST
TREND = LINTREND
TRIMMEAN = TRIMMEAN
VAR.P = VAR.P
VAR.S = VAR.S
VARA = VARA
VARPA = VARPA
WEIBULL.DIST = WEIBULL.DIST
Z.TEST = Z.TEST

##
## Textové funkce (Text Functions)
##
BAHTTEXT = BAHTTEXT
CHAR = ZNAK
CLEAN = VYČISTIT
CODE = KÓD
CONCAT = CONCAT
DOLLAR = KČ
EXACT = STEJNÉ
FIND = NAJÍT
FIXED = ZAOKROUHLIT.NA.TEXT
LEFT = ZLEVA
LEN = DÉLKA
LOWER = MALÁ
MID = ČÁST
NUMBERVALUE = NUMBERVALUE
PHONETIC = ZVUKOVÉ
PROPER = VELKÁ2
REPLACE = NAHRADIT
REPT = OPAKOVAT
RIGHT = ZPRAVA
SEARCH = HLEDAT
SUBSTITUTE = DOSADIT
T = T
TEXT = HODNOTA.NA.TEXT
TEXTJOIN = TEXTJOIN
TRIM = PROČISTIT
UNICHAR = UNICHAR
UNICODE = UNICODE
UPPER = VELKÁ
VALUE = HODNOTA

##
## Webové funkce (Web Functions)
##
ENCODEURL = ENCODEURL
FILTERXML = FILTERXML
WEBSERVICE = WEBSERVICE

##
## Funkce pro kompatibilitu (Compatibility Functions)
##
BETADIST = BETADIST
BETAINV = BETAINV
BINOMDIST = BINOMDIST
CEILING = ZAOKR.NAHORU
CHIDIST = CHIDIST
CHIINV = CHIINV
CHITEST = CHITEST
CONCATENATE = CONCATENATE
CONFIDENCE = CONFIDENCE
COVAR = COVAR
CRITBINOM = CRITBINOM
EXPONDIST = EXPONDIST
FDIST = FDIST
FINV = FINV
FLOOR = ZAOKR.DOLŮ
FORECAST = FORECAST
FTEST = FTEST
GAMMADIST = GAMMADIST
GAMMAINV = GAMMAINV
HYPGEOMDIST = HYPGEOMDIST
LOGINV = LOGINV
LOGNORMDIST = LOGNORMDIST
MODE = MODE
NEGBINOMDIST = NEGBINOMDIST
NORMDIST = NORMDIST
NORMINV = NORMINV
NORMSDIST = NORMSDIST
NORMSINV = NORMSINV
PERCENTILE = PERCENTIL
PERCENTRANK = PERCENTRANK
POISSON = POISSON
QUARTILE = QUARTIL
RANK = RANK
STDEV = SMODCH.VÝBĚR
STDEVP = SMODCH
TDIST = TDIST
TINV = TINV
TTEST = TTEST
VAR = VAR.VÝBĚR
VARP = VAR
WEIBULL = WEIBULL
ZTEST = ZTEST
