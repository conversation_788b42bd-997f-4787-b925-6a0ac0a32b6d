{"name": "rap2hpoutre/laravel-log-viewer", "description": "A Laravel log reader", "license": "MIT", "keywords": ["log", "log-reader", "log-viewer", "logging", "laravel", "lumen"], "type": "laravel-package", "authors": [{"name": "rap2hpoutre", "email": "rap<PERSON><PERSON><PERSON>@gmail.com"}], "require": {"php": "^7.2|^8.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0"}, "require-dev": {"phpunit/phpunit": "^7||^8.4|^9.3.3|^10.1|^11.0", "orchestra/testbench": "^4.0|^5.0|^6.0|^7.0|^8.0|^9.0|^10.0"}, "autoload": {"classmap": ["src/controllers"], "psr-0": {"Rap2hpoutre\\LaravelLogViewer\\": "src/"}}, "autoload-dev": {"psr-4": {"Rap2hpoutre\\LaravelLogViewer\\Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["Rap2hpoutre\\LaravelLogViewer\\LaravelLogViewerServiceProvider"]}}, "minimum-stability": "stable"}