<?php declare(strict_types=1);
/*
 * This file is part of phpunit/php-code-coverage.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\Driver;

use RuntimeException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\Exception;

final class XdebugNotEnabledException extends RuntimeException implements Exception
{
    public function __construct()
    {
        parent::__construct('XDEBUG_MODE=coverage (environment variable) or xdebug.mode=coverage (PHP configuration setting) has to be set');
    }
}
