{"name": "illuminate/testing", "description": "The Illuminate Testing package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-mbstring": "*", "illuminate/collections": "^11.0", "illuminate/contracts": "^11.0", "illuminate/macroable": "^11.0", "illuminate/support": "^11.0"}, "autoload": {"psr-4": {"Illuminate\\Testing\\": ""}}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}}, "suggest": {"brianium/paratest": "Required to run tests in parallel (^7.0|^8.0).", "illuminate/console": "Required to assert console commands (^11.0).", "illuminate/database": "Required to assert databases (^11.0).", "illuminate/http": "Required to assert responses (^11.0).", "mockery/mockery": "Required to use mocking (^1.6).", "phpunit/phpunit": "Required to use assertions and run tests (^10.5.35|^11.3.6|^12.0.1)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}