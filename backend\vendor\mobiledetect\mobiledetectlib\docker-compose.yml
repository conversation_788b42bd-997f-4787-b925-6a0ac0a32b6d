services:
  setup:
    build:
      context: .
      dockerfile: ./docker/Dockerfile.setup

  runUnitTests:
    # Need xdebug from this image to run with coverage
    # https://hub.docker.com/r/alcohol/php/tags
    image: alcohol/php:8.3-xdebug
    depends_on:
      setup:
        condition: service_completed_successfully
    working_dir: /app
    environment:
      XDEBUG_MODE: coverage
    command: >
      /bin/sh -c "vendor/bin/phpunit -v -c tests/phpunit.xml --coverage-html ./coverage --strict-coverage --stop-on-risky"
    volumes:
      - .:/app

  runPerfTests:
    image: php:8.3-rc-alpine3.18
    depends_on:
      setup:
        condition: service_completed_successfully
    working_dir: /app
    command: >
      /bin/sh -c "vendor/bin/phpbench run tests/benchmark/MobileDetectBench.php --retry-threshold=1 --iterations=10 --revs=1000 --report=aggregate"
    volumes:
      - .:/app

  runLinting:
    image: php:8.3-rc-alpine3.18
    depends_on:
      setup:
        condition: service_completed_successfully
    working_dir: /app
    command: >
      /bin/sh -c "vendor/bin/phpcs; vendor/bin/phpcbf"
    volumes:
      - .:/app

  generateModel:
    image: php:8.3-rc-alpine3.18
    depends_on:
      setup:
        condition: service_completed_successfully
      runUnitTests:
        condition: service_completed_successfully
      runPerfTests:
        condition: service_completed_successfully
      runLinting:
        condition: service_completed_successfully

    working_dir: /app
    command: >
      /bin/sh -c "php ./scripts/export_to_json.php"
    volumes:
      - .:/app


