<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cheque_statements', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('payment_id')->index();
            $table->string('voucher_no');
            $table->unsignedBigInteger('transaction_id');
            $table->string('transaction_type');
            $table->string('reference_no')->nullable();
            $table->enum('refer_type', ['Customer', 'Supplier', 'Ledger']);
            $table->unsignedBigInteger('refer_id')->nullable();
            $table->string('refer_name');
            $table->decimal('amount', 15, 2);
            $table->date('payment_date');
            $table->string('cheque_no');
            $table->string('bank_name');
            $table->date('issue_date');
            $table->text('note')->nullable();
            $table->enum('status', ['pending', 'completed', 'declined'])->default('pending');
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('payment_id')->references('id')->on('payments')->onDelete('cascade');
            
            // Indexes for performance
            $table->index(['refer_type', 'refer_id']);
            $table->index('status');
            $table->index('payment_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cheque_statements');
    }
};
