<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\ChequeStatement;
use App\Models\AccountSubGroup;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ProfitAndLossReportController extends Controller
{
    public function generateProfitAndLoss(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
        ]);

        $fromDate = Carbon::parse($request->from_date)->startOfDay();
        $toDate = Carbon::parse($request->to_date)->endOfDay();

        try {
            // Initialize income and expense arrays
            $income = [];
            $expenses = [];

            // New logic: Calculate income and expenses from invoice and sale items considering opening stock
            $productStockUsage = []; // Track opening stock usage per product_variant_id

            // Fetch invoice items within date range excluding deleted invoices
            $invoiceItems = \App\Models\InvoiceItem::whereHas('invoice', function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('invoice_date', [$fromDate, $toDate])
                      ->whereNull('deleted_by')
                      ->whereNull('deleted_at');
            })->get();

            // Process invoice items for income and expense
            foreach ($invoiceItems as $item) {
                $variantId = $item->product_variant_id;
                $quantity = $item->quantity;
                $sellingPrice = $item->sales_price ?? 0;
                $buyingCost = $item->total_buying_cost ?? 0;

                // Initialize stock usage if not set
                if (!isset($productStockUsage[$variantId])) {
                    $variant = $item->productVariant;
                    $productStockUsage[$variantId] = $variant ? $variant->opening_stock_quantity : 0;
                }

                // Calculate quantity from opening stock and after opening stock
                $openingStockQty = $productStockUsage[$variantId];
                $qtyFromOpeningStock = min($quantity, $openingStockQty);
                $qtyAfterOpeningStock = max(0, $quantity - $openingStockQty);

                // Update stock usage
                $productStockUsage[$variantId] -= $qtyFromOpeningStock;

                // Income: selling price * total quantity
                // $income[] = [
                //     'category' => 'Sales/Invoice',
                //     'description' => "Invoice: {$item->invoice->invoice_no}",
                //     'amount' => abs($sellingPrice * $quantity),
                //     'date' => $item->invoice->invoice_date->format('Y-m-d'),
                // ];

                // Expense: buying cost * quantity from opening stock only
                if ($qtyFromOpeningStock > 0) {
                    $expenseAmount = 0;
                    if ($quantity > 0) {
                        $unitBuyingCost = $buyingCost / $quantity;
                        $expenseAmount = $unitBuyingCost * $qtyFromOpeningStock;
                    }
                    $expenses[] = [
                        'category' => 'Cost of Goods Sold',
                        'description' => "Invoice Cost: {$item->invoice->invoice_no}",
                        'amount' => abs($expenseAmount),
                        'date' => $item->invoice->invoice_date->format('Y-m-d'),
                    ];
                }
            }

            // Fetch sale items within date range excluding deleted sales
            $saleItems = \App\Models\SaleItem::whereHas('sale', function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('created_at', [$fromDate, $toDate])
                      ->whereNull('deleted_by')
                      ->whereNull('deleted_at');
            })->get();

            // Process sale items for income and expense
            foreach ($saleItems as $item) {
                $variantId = $item->product_variant_id;
                $quantity = $item->quantity;
                $sellingPrice = $item->mrp ?? 0; // Assuming MRP as selling price for sales
                $buyingCost = 0;

                // Try to get buying cost from product variant
                $variant = $item->productVariant;
                if ($variant) {
                    $buyingCost = $variant->buying_cost ?? 0;
                }

                // Initialize stock usage if not set
                if (!isset($productStockUsage[$variantId])) {
                    $productStockUsage[$variantId] = $variant ? $variant->opening_stock_quantity : 0;
                }

                // Calculate quantity from opening stock and after opening stock
                $openingStockQty = $productStockUsage[$variantId];
                $qtyFromOpeningStock = min($quantity, $openingStockQty);
                $qtyAfterOpeningStock = max(0, $quantity - $openingStockQty);

                // Update stock usage
                $productStockUsage[$variantId] -= $qtyFromOpeningStock;

                // Income: selling price * total quantity
                // $income[] = [
                //     'category' => 'Sales',
                //     'description' => "Sale: {$item->sale->bill_number}",
                //     'amount' => abs($sellingPrice * $quantity),
                //     'date' => $item->sale->created_at->format('Y-m-d'),
                // ];

                // Expense: buying cost * quantity from opening stock only
                if ($qtyFromOpeningStock > 0) {
                    $expenseAmount = $buyingCost * $qtyFromOpeningStock;
                    $expenses[] = [
                        'category' => 'Cost of Goods Sold',
                        'description' => "Sale Cost: {$item->sale->bill_number}",
                        'amount' => abs($expenseAmount),
                        'date' => $item->sale->created_at->format('Y-m-d'),
                    ];
                }
            }

            // Fetch purchases within date range excluding deleted purchases
            // $purchases = \App\Models\Purchase::whereBetween('date_of_purchase', [$fromDate, $toDate])
            //     ->whereNull('deleted_by')
            //     ->whereNull('deleted_at')
            //     ->with('items')
            //     ->get();

            // Process purchases for expense
            // foreach ($purchases as $purchase) {
            //     foreach ($purchase->items as $item) {
            //         $quantity = $item->quantity;
            //         $buyingCost = $item->buying_cost ?? 0;
            //         $expenseAmount = $buyingCost * $quantity;

            //         $expenses[] = [
            //             'category' => 'Purchase',
            //             'description' => "Purchase: {$purchase->bill_number}",
            //             'amount' => abs($expenseAmount),
            //             'date' => $purchase->date_of_purchase->format('Y-m-d'),
            //         ];
            //     }
            // }

            // Process PaymentMethod transactions (sales, invoices, and purchases)
            $paymentMethodTransactions = PaymentMethod::whereBetween('date', [$fromDate, $toDate])
                ->get();

            $incomeMainAccounts = ['Direct Income', 'Indirect Income', 'Sales Accounts'];
            $expenseMainAccounts = ['Direct Expenses', 'Indirect Expenses', 'Purchase Account'];

            foreach ($paymentMethodTransactions as $transaction) {
                $accountGroup = $this->getMainAccountType($transaction->account_group);
                $referenceNumber = $transaction->reference_number ?? 'N/A';

                // --- NEW LOGIC: Exclude soft-deleted invoices for invoice type transactions ---
                $isInvoiceType = strtolower($transaction->type) === 'invoice';
                $isSalesType = strtolower($transaction->type) === 'sales';
                $isPurchaseType = strtolower($transaction->type) === 'purchase';
                $includeTransaction = true;
                if ($isInvoiceType) {
                    $invoice = \App\Models\Invoice::where('invoice_no', $referenceNumber)
                        ->whereNull('deleted_at')
                        ->whereNull('deleted_by')
                        ->first();
                    if (!$invoice) {
                        $includeTransaction = false;
                    }
                }
                // Optionally, you can do similar for sales/purchase if needed

                if (($isSalesType || $isInvoiceType) && $includeTransaction) {
                    $income[] = [
                        'category' => 'Sales/Invoice',
                        'description' => ucfirst($transaction->type) . ": {$referenceNumber}",
                        'amount' => abs((float)$transaction->total),
                        'date' => $transaction->date->format('Y-m-d'),
                    ];
                } elseif ($isPurchaseType) {
                    $expenses[] = [
                        'category' => 'Purchase',
                        'description' => "Purchase: {$referenceNumber}",
                        'amount' => abs((float)$transaction->total),
                        'date' => $transaction->date->format('Y-m-d'),
                    ];
                }
            }

            // Process Payment transactions (vouchers)
            $paymentTransactions = Payment::whereBetween('payment_date', [$fromDate, $toDate])
                ->get();

            foreach ($paymentTransactions as $payment) {
                $accountGroup = $this->getMainAccountType($payment->account_type ?? 'Direct Expenses');
                $subAccountName = $this->getSubAccountName($payment->account_type);
                $description = $subAccountName ?: $accountGroup;

                // --- EXISTING LOGIC FOR LEDGER VOUCHERS ---
                if (strpos($payment->voucher_no, 'REC-') === 0 && $payment->refer_type === 'Ledger' && in_array($accountGroup, $incomeMainAccounts)) {
                    $income[] = [
                        'category' => $accountGroup,
                        'description' => "Receive Voucher: {$description} ({$payment->voucher_no})",
                        'amount' => abs((float)$payment->amount),
                        'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                    ];
                } elseif (strpos($payment->voucher_no, 'PAY-') === 0 && $payment->refer_type === 'Ledger' && in_array($accountGroup, $expenseMainAccounts)) {
                    $expenses[] = [
                        'category' => $accountGroup,
                        'description' => "Payment Voucher: {$description} ({$payment->voucher_no})",
                        'amount' => abs((float)$payment->amount),
                        'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                    ];
                }

                // --- NEW LOGIC FOR DISCOUNTS ---
                // Receive Voucher to Customer
                if (strpos($payment->voucher_no, 'REC-') === 0 && $payment->refer_type === 'Customer' && $payment->discount > 0) {
                    if (strtolower($payment->payment_method) === 'cheque') {
                        // Check if declined
                        $declinedCheque = \App\Models\ChequeStatement::where('payment_id', $payment->id)->where('status', 'declined')->first();
                        if ($declinedCheque) {
                            $income[] = [
                                'category' => 'Discount (Declined Cheque)',
                                'description' => "Discount reversed from declined cheque to customer (Voucher: {$payment->voucher_no})",
                                'amount' => abs((float)$payment->discount),
                                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                            ];
                        } else {
                            $expenses[] = [
                                'category' => 'Discount',
                                'description' => "Discount given to customer (Voucher: {$payment->voucher_no})",
                                'amount' => abs((float)$payment->discount),
                                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                            ];
                        }
                    } else {
                        $expenses[] = [
                            'category' => 'Discount',
                            'description' => "Discount given to customer (Voucher: {$payment->voucher_no})",
                            'amount' => abs((float)$payment->discount),
                            'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    }
                }
                // Payment Voucher to Supplier
                if (strpos($payment->voucher_no, 'PAY-') === 0 && $payment->refer_type === 'Supplier' && $payment->discount > 0) {
                    if (strtolower($payment->payment_method) === 'cheque') {
                        // Check if declined
                        $declinedCheque = \App\Models\ChequeStatement::where('payment_id', $payment->id)->where('status', 'declined')->first();
                        // Always add to income
                        $income[] = [
                            'category' => 'Discount',
                            'description' => "Discount received from supplier (Voucher: {$payment->voucher_no})",
                            'amount' => abs((float)$payment->discount),
                            'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                        ];
                        // If declined, also add to expense
                        if ($declinedCheque) {
                            $expenses[] = [
                                'category' => 'Discount (Declined Cheque)',
                                'description' => "Discount reversed from declined cheque to supplier (Voucher: {$payment->voucher_no})",
                                'amount' => abs((float)$payment->discount),
                                'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                            ];
                        }
                    } else {
                        $income[] = [
                            'category' => 'Discount',
                            'description' => "Discount received from supplier (Voucher: {$payment->voucher_no})",
                            'amount' => abs((float)$payment->discount),
                            'date' => $payment->payment_date ? $payment->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    }
                }
            }

            // Process ChequeStatement transactions (declined vouchers)
            $chequeTransactions = ChequeStatement::whereBetween('payment_date', [$fromDate, $toDate])
                ->where('status', 'declined')
                ->get();

            foreach ($chequeTransactions as $cheque) {
                $accountGroup = $this->getMainAccountType($cheque->account_group ?? 'Direct Expenses');
                $subAccountName = $this->getSubAccountName($cheque->account_group);
                $description = $subAccountName ?: $accountGroup;

                // Only include if refer_type is 'Ledger'
                if (($cheque->refer_type ?? null) === 'Ledger') {
                    if (strpos($cheque->voucher_no, 'PAY-') === 0 && in_array($accountGroup, $incomeMainAccounts)) {
                        $income[] = [
                            'category' => $accountGroup,
                            'description' => "Declined Payment Voucher: {$description} ({$cheque->voucher_no})",
                            'amount' => abs((float)$cheque->amount),
                            'date' => $cheque->payment_date ? $cheque->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    } elseif (strpos($cheque->voucher_no, 'REC-') === 0 && in_array($accountGroup, $expenseMainAccounts)) {
                        $expenses[] = [
                            'category' => $accountGroup,
                            'description' => "Declined Receive Voucher: {$description} ({$cheque->voucher_no})",
                            'amount' => abs((float)$cheque->amount),
                            'date' => $cheque->payment_date ? $cheque->payment_date->format('Y-m-d') : 'N/A',
                        ];
                    }
                }
            }

            // Calculate totals
            $totalIncome = array_sum(array_column($income, 'amount'));
            $totalExpenses = array_sum(array_column($expenses, 'amount'));
            $netProfitLoss = $totalIncome - $totalExpenses;

            return response()->json([
                'success' => true,
                'data' => [
                    'income' => $income,
                    'expenses' => $expenses,
                ],
                'totals' => [
                    'totalIncome' => $totalIncome,
                    'totalExpenses' => $totalExpenses,
                    'netProfitLoss' => $netProfitLoss,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating profit and loss report: ' . $e->getMessage(),
            ], 500);
        }
    }

    private function getMainAccountTypes()
    {
        return [
            'Direct Expenses', 'Indirect Expenses', 'Indirect Income', 'Loan Liabilities',
            'Bank OD', 'Current Liabilities', 'Sundry Creditors', 'Capital Account',
            'Bank Accounts', 'Current Asset', 'Sundry Debtors', 'Fixed Asset', 'Stock in hand',
            'Purchase Account', 'Sales Accounts', 'Cash in Hand'
        ];
    }

    private function getMainAccountType($accountGroup)
    {
        $mainAccountGroups = $this->getMainAccountTypes();

        if (in_array($accountGroup, $mainAccountGroups)) {
            return $accountGroup;
        }

        $subGroup = AccountSubGroup::where('sub_group_name', $accountGroup)->first();
        return $subGroup ? $subGroup->main_group : 'Direct Expenses';
    }

    private function isDebitAccountType($mainAccountType)
    {
        $debitAccountTypes = [
            'Direct Expenses', 'Indirect Expenses', 'Bank Accounts',
            'Current Asset', 'Sundry Debtors', 'Fixed Asset', 'Stock in hand',
            'Purchase Account' ,'Cash in Hand'
        ];

        return in_array($mainAccountType, $debitAccountTypes);
    }

    private function getSubAccountName($accountGroup)
    {
        $subGroup = AccountSubGroup::where('sub_group_name', $accountGroup)->first();
        return $subGroup ? $subGroup->sub_group_name : null;
    }
}