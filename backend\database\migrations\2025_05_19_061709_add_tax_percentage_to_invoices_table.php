<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTaxPercentageToInvoicesTable extends Migration
{
    public function up()
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Add tax_percentage as a decimal with 5 digits total, 2 after decimal (e.g., 12.34)
            $table->decimal('tax_percentage', 5, 2)->nullable()->after('subtotal');
        });
    }

    public function down()
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn('tax_percentage');
        });
    }
}