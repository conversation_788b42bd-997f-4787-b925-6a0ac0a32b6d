############################################################
##
## PhpSpreadsheet - function name translations
##
## Français (French)
##
############################################################


##
## Fonctions Cube (Cube Functions)
##
CUBEKPIMEMBER = MEMBREKPICUBE
CUBEMEMBER = MEMBRECUBE
CUBEMEMBERPROPERTY = PROPRIETEMEMBRECUBE
CUBERANKEDMEMBER = RANGMEMBRECUBE
CUBESET = JEUCUBE
CUBESETCOUNT = NBJEUCUBE
CUBEVALUE = VALEURCUBE

##
## Fonctions de base de données (Database Functions)
##
DAVERAGE = BDMOYENNE
DCOUNT = BDNB
DCOUNTA = BDNBVAL
DGET = BDLIRE
DMAX = BDMAX
DMIN = BDMIN
DPRODUCT = BDPRODUIT
DSTDEV = BDECARTYPE
DSTDEVP = BDECARTYPEP
DSUM = BDSOMME
DVAR = BDVAR
DVARP = BDVARP

##
## Fonctions de date et d’heure (Date & Time Functions)
##
DATE = DATE
DATEVALUE = DATEVAL
DAY = JOUR
DAYS = JOURS
DAYS360 = JOURS360
EDATE = MOIS.DECALER
EOMONTH = FIN.MOIS
HOUR = HEURE
ISOWEEKNUM = NO.SEMAINE.ISO
MINUTE = MINUTE
MONTH = MOIS
NETWORKDAYS = NB.JOURS.OUVRES
NETWORKDAYS.INTL = NB.JOURS.OUVRES.INTL
NOW = MAINTENANT
SECOND = SECONDE
TIME = TEMPS
TIMEVALUE = TEMPSVAL
TODAY = AUJOURDHUI
WEEKDAY = JOURSEM
WEEKNUM = NO.SEMAINE
WORKDAY = SERIE.JOUR.OUVRE
WORKDAY.INTL = SERIE.JOUR.OUVRE.INTL
YEAR = ANNEE
YEARFRAC = FRACTION.ANNEE

##
## Fonctions d’ingénierie (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BINDEC
BIN2HEX = BINHEX
BIN2OCT = BINOCT
BITAND = BITET
BITLSHIFT = BITDECALG
BITOR = BITOU
BITRSHIFT = BITDECALD
BITXOR = BITOUEXCLUSIF
COMPLEX = COMPLEXE
CONVERT = CONVERT
DEC2BIN = DECBIN
DEC2HEX = DECHEX
DEC2OCT = DECOCT
DELTA = DELTA
ERF = ERF
ERF.PRECISE = ERF.PRECIS
ERFC = ERFC
ERFC.PRECISE = ERFC.PRECIS
GESTEP = SUP.SEUIL
HEX2BIN = HEXBIN
HEX2DEC = HEXDEC
HEX2OCT = HEXOCT
IMABS = COMPLEXE.MODULE
IMAGINARY = COMPLEXE.IMAGINAIRE
IMARGUMENT = COMPLEXE.ARGUMENT
IMCONJUGATE = COMPLEXE.CONJUGUE
IMCOS = COMPLEXE.COS
IMCOSH = COMPLEXE.COSH
IMCOT = COMPLEXE.COT
IMCSC = COMPLEXE.CSC
IMCSCH = COMPLEXE.CSCH
IMDIV = COMPLEXE.DIV
IMEXP = COMPLEXE.EXP
IMLN = COMPLEXE.LN
IMLOG10 = COMPLEXE.LOG10
IMLOG2 = COMPLEXE.LOG2
IMPOWER = COMPLEXE.PUISSANCE
IMPRODUCT = COMPLEXE.PRODUIT
IMREAL = COMPLEXE.REEL
IMSEC = COMPLEXE.SEC
IMSECH = COMPLEXE.SECH
IMSIN = COMPLEXE.SIN
IMSINH = COMPLEXE.SINH
IMSQRT = COMPLEXE.RACINE
IMSUB = COMPLEXE.DIFFERENCE
IMSUM = COMPLEXE.SOMME
IMTAN = COMPLEXE.TAN
OCT2BIN = OCTBIN
OCT2DEC = OCTDEC
OCT2HEX = OCTHEX

##
## Fonctions financières (Financial Functions)
##
ACCRINT = INTERET.ACC
ACCRINTM = INTERET.ACC.MAT
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = NB.JOURS.COUPON.PREC
COUPDAYS = NB.JOURS.COUPONS
COUPDAYSNC = NB.JOURS.COUPON.SUIV
COUPNCD = DATE.COUPON.SUIV
COUPNUM = NB.COUPONS
COUPPCD = DATE.COUPON.PREC
CUMIPMT = CUMUL.INTER
CUMPRINC = CUMUL.PRINCPER
DB = DB
DDB = DDB
DISC = TAUX.ESCOMPTE
DOLLARDE = PRIX.DEC
DOLLARFR = PRIX.FRAC
DURATION = DUREE
EFFECT = TAUX.EFFECTIF
FV = VC
FVSCHEDULE = VC.PAIEMENTS
INTRATE = TAUX.INTERET
IPMT = INTPER
IRR = TRI
ISPMT = ISPMT
MDURATION = DUREE.MODIFIEE
MIRR = TRIM
NOMINAL = TAUX.NOMINAL
NPER = NPM
NPV = VAN
ODDFPRICE = PRIX.PCOUPON.IRREG
ODDFYIELD = REND.PCOUPON.IRREG
ODDLPRICE = PRIX.DCOUPON.IRREG
ODDLYIELD = REND.DCOUPON.IRREG
PDURATION = PDUREE
PMT = VPM
PPMT = PRINCPER
PRICE = PRIX.TITRE
PRICEDISC = VALEUR.ENCAISSEMENT
PRICEMAT = PRIX.TITRE.ECHEANCE
PV = VA
RATE = TAUX
RECEIVED = VALEUR.NOMINALE
RRI = TAUX.INT.EQUIV
SLN = AMORLIN
SYD = SYD
TBILLEQ = TAUX.ESCOMPTE.R
TBILLPRICE = PRIX.BON.TRESOR
TBILLYIELD = RENDEMENT.BON.TRESOR
VDB = VDB
XIRR = TRI.PAIEMENTS
XNPV = VAN.PAIEMENTS
YIELD = RENDEMENT.TITRE
YIELDDISC = RENDEMENT.SIMPLE
YIELDMAT = RENDEMENT.TITRE.ECHEANCE

##
## Fonctions d’information (Information Functions)
##
CELL = CELLULE
ERROR.TYPE = TYPE.ERREUR
INFO = INFORMATIONS
ISBLANK = ESTVIDE
ISERR = ESTERR
ISERROR = ESTERREUR
ISEVEN = EST.PAIR
ISFORMULA = ESTFORMULE
ISLOGICAL = ESTLOGIQUE
ISNA = ESTNA
ISNONTEXT = ESTNONTEXTE
ISNUMBER = ESTNUM
ISODD = EST.IMPAIR
ISREF = ESTREF
ISTEXT = ESTTEXTE
N = N
NA = NA
SHEET = FEUILLE
SHEETS = FEUILLES
TYPE = TYPE

##
## Fonctions logiques (Logical Functions)
##
AND = ET
FALSE = FAUX
IF = SI
IFERROR = SIERREUR
IFNA = SI.NON.DISP
IFS = SI.CONDITIONS
NOT = NON
OR = OU
SWITCH = SI.MULTIPLE
TRUE = VRAI
XOR = OUX

##
## Fonctions de recherche et de référence (Lookup & Reference Functions)
##
ADDRESS = ADRESSE
AREAS = ZONES
CHOOSE = CHOISIR
COLUMN = COLONNE
COLUMNS = COLONNES
FORMULATEXT = FORMULETEXTE
GETPIVOTDATA = LIREDONNEESTABCROISDYNAMIQUE
HLOOKUP = RECHERCHEH
HYPERLINK = LIEN_HYPERTEXTE
INDEX = INDEX
INDIRECT = INDIRECT
LOOKUP = RECHERCHE
MATCH = EQUIV
OFFSET = DECALER
ROW = LIGNE
ROWS = LIGNES
RTD = RTD
TRANSPOSE = TRANSPOSE
VLOOKUP = RECHERCHEV
*RC = LC

##
## Fonctions mathématiques et trigonométriques (Math & Trig Functions)
##
ABS = ABS
ACOS = ACOS
ACOSH = ACOSH
ACOT = ACOT
ACOTH = ACOTH
AGGREGATE = AGREGAT
ARABIC = CHIFFRE.ARABE
ASIN = ASIN
ASINH = ASINH
ATAN = ATAN
ATAN2 = ATAN2
ATANH = ATANH
BASE = BASE
CEILING.MATH = PLAFOND.MATH
CEILING.PRECISE = PLAFOND.PRECIS
COMBIN = COMBIN
COMBINA = COMBINA
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = DECIMAL
DEGREES = DEGRES
ECMA.CEILING = ECMA.PLAFOND
EVEN = PAIR
EXP = EXP
FACT = FACT
FACTDOUBLE = FACTDOUBLE
FLOOR.MATH = PLANCHER.MATH
FLOOR.PRECISE = PLANCHER.PRECIS
GCD = PGCD
INT = ENT
ISO.CEILING = ISO.PLAFOND
LCM = PPCM
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = DETERMAT
MINVERSE = INVERSEMAT
MMULT = PRODUITMAT
MOD = MOD
MROUND = ARRONDI.AU.MULTIPLE
MULTINOMIAL = MULTINOMIALE
MUNIT = MATRICE.UNITAIRE
ODD = IMPAIR
PI = PI
POWER = PUISSANCE
PRODUCT = PRODUIT
QUOTIENT = QUOTIENT
RADIANS = RADIANS
RAND = ALEA
RANDBETWEEN = ALEA.ENTRE.BORNES
ROMAN = ROMAIN
ROUND = ARRONDI
ROUNDDOWN = ARRONDI.INF
ROUNDUP = ARRONDI.SUP
SEC = SEC
SECH = SECH
SERIESSUM = SOMME.SERIES
SIGN = SIGNE
SIN = SIN
SINH = SINH
SQRT = RACINE
SQRTPI = RACINE.PI
SUBTOTAL = SOUS.TOTAL
SUM = SOMME
SUMIF = SOMME.SI
SUMIFS = SOMME.SI.ENS
SUMPRODUCT = SOMMEPROD
SUMSQ = SOMME.CARRES
SUMX2MY2 = SOMME.X2MY2
SUMX2PY2 = SOMME.X2PY2
SUMXMY2 = SOMME.XMY2
TAN = TAN
TANH = TANH
TRUNC = TRONQUE

##
## Fonctions statistiques (Statistical Functions)
##
AVEDEV = ECART.MOYEN
AVERAGE = MOYENNE
AVERAGEA = AVERAGEA
AVERAGEIF = MOYENNE.SI
AVERAGEIFS = MOYENNE.SI.ENS
BETA.DIST = LOI.BETA.N
BETA.INV = BETA.INVERSE.N
BINOM.DIST = LOI.BINOMIALE.N
BINOM.DIST.RANGE = LOI.BINOMIALE.SERIE
BINOM.INV = LOI.BINOMIALE.INVERSE
CHISQ.DIST = LOI.KHIDEUX.N
CHISQ.DIST.RT = LOI.KHIDEUX.DROITE
CHISQ.INV = LOI.KHIDEUX.INVERSE
CHISQ.INV.RT = LOI.KHIDEUX.INVERSE.DROITE
CHISQ.TEST = CHISQ.TEST
CONFIDENCE.NORM = INTERVALLE.CONFIANCE.NORMAL
CONFIDENCE.T = INTERVALLE.CONFIANCE.STUDENT
CORREL = COEFFICIENT.CORRELATION
COUNT = NB
COUNTA = NBVAL
COUNTBLANK = NB.VIDE
COUNTIF = NB.SI
COUNTIFS = NB.SI.ENS
COVARIANCE.P = COVARIANCE.PEARSON
COVARIANCE.S = COVARIANCE.STANDARD
DEVSQ = SOMME.CARRES.ECARTS
EXPON.DIST = LOI.EXPONENTIELLE.N
F.DIST = LOI.F.N
F.DIST.RT = LOI.F.DROITE
F.INV = INVERSE.LOI.F.N
F.INV.RT = INVERSE.LOI.F.DROITE
F.TEST = F.TEST
FISHER = FISHER
FISHERINV = FISHER.INVERSE
FORECAST.ETS = PREVISION.ETS
FORECAST.ETS.CONFINT = PREVISION.ETS.CONFINT
FORECAST.ETS.SEASONALITY = PREVISION.ETS.CARACTERESAISONNIER
FORECAST.ETS.STAT = PREVISION.ETS.STAT
FORECAST.LINEAR = PREVISION.LINEAIRE
FREQUENCY = FREQUENCE
GAMMA = GAMMA
GAMMA.DIST = LOI.GAMMA.N
GAMMA.INV = LOI.GAMMA.INVERSE.N
GAMMALN = LNGAMMA
GAMMALN.PRECISE = LNGAMMA.PRECIS
GAUSS = GAUSS
GEOMEAN = MOYENNE.GEOMETRIQUE
GROWTH = CROISSANCE
HARMEAN = MOYENNE.HARMONIQUE
HYPGEOM.DIST = LOI.HYPERGEOMETRIQUE.N
INTERCEPT = ORDONNEE.ORIGINE
KURT = KURTOSIS
LARGE = GRANDE.VALEUR
LINEST = DROITEREG
LOGEST = LOGREG
LOGNORM.DIST = LOI.LOGNORMALE.N
LOGNORM.INV = LOI.LOGNORMALE.INVERSE.N
MAX = MAX
MAXA = MAXA
MAXIFS = MAX.SI
MEDIAN = MEDIANE
MIN = MIN
MINA = MINA
MINIFS = MIN.SI
MODE.MULT = MODE.MULTIPLE
MODE.SNGL = MODE.SIMPLE
NEGBINOM.DIST = LOI.BINOMIALE.NEG.N
NORM.DIST = LOI.NORMALE.N
NORM.INV = LOI.NORMALE.INVERSE.N
NORM.S.DIST = LOI.NORMALE.STANDARD.N
NORM.S.INV = LOI.NORMALE.STANDARD.INVERSE.N
PEARSON = PEARSON
PERCENTILE.EXC = CENTILE.EXCLURE
PERCENTILE.INC = CENTILE.INCLURE
PERCENTRANK.EXC = RANG.POURCENTAGE.EXCLURE
PERCENTRANK.INC = RANG.POURCENTAGE.INCLURE
PERMUT = PERMUTATION
PERMUTATIONA = PERMUTATIONA
PHI = PHI
POISSON.DIST = LOI.POISSON.N
PROB = PROBABILITE
QUARTILE.EXC = QUARTILE.EXCLURE
QUARTILE.INC = QUARTILE.INCLURE
RANK.AVG = MOYENNE.RANG
RANK.EQ = EQUATION.RANG
RSQ = COEFFICIENT.DETERMINATION
SKEW = COEFFICIENT.ASYMETRIE
SKEW.P = COEFFICIENT.ASYMETRIE.P
SLOPE = PENTE
SMALL = PETITE.VALEUR
STANDARDIZE = CENTREE.REDUITE
STDEV.P = ECARTYPE.PEARSON
STDEV.S = ECARTYPE.STANDARD
STDEVA = STDEVA
STDEVPA = STDEVPA
STEYX = ERREUR.TYPE.XY
T.DIST = LOI.STUDENT.N
T.DIST.2T = LOI.STUDENT.BILATERALE
T.DIST.RT = LOI.STUDENT.DROITE
T.INV = LOI.STUDENT.INVERSE.N
T.INV.2T = LOI.STUDENT.INVERSE.BILATERALE
T.TEST = T.TEST
TREND = TENDANCE
TRIMMEAN = MOYENNE.REDUITE
VAR.P = VAR.P.N
VAR.S = VAR.S
VARA = VARA
VARPA = VARPA
WEIBULL.DIST = LOI.WEIBULL.N
Z.TEST = Z.TEST

##
## Fonctions de texte (Text Functions)
##
BAHTTEXT = BAHTTEXT
CHAR = CAR
CLEAN = EPURAGE
CODE = CODE
CONCAT = CONCAT
DOLLAR = DEVISE
EXACT = EXACT
FIND = TROUVE
FIXED = CTXT
LEFT = GAUCHE
LEN = NBCAR
LOWER = MINUSCULE
MID = STXT
NUMBERVALUE = VALEURNOMBRE
PHONETIC = PHONETIQUE
PROPER = NOMPROPRE
REPLACE = REMPLACER
REPT = REPT
RIGHT = DROITE
SEARCH = CHERCHE
SUBSTITUTE = SUBSTITUE
T = T
TEXT = TEXTE
TEXTJOIN = JOINDRE.TEXTE
TRIM = SUPPRESPACE
UNICHAR = UNICAR
UNICODE = UNICODE
UPPER = MAJUSCULE
VALUE = CNUM

##
## Fonctions web (Web Functions)
##
ENCODEURL = URLENCODAGE
FILTERXML = FILTRE.XML
WEBSERVICE = SERVICEWEB

##
## Fonctions de compatibilité (Compatibility Functions)
##
BETADIST = LOI.BETA
BETAINV = BETA.INVERSE
BINOMDIST = LOI.BINOMIALE
CEILING = PLAFOND
CHIDIST = LOI.KHIDEUX
CHIINV = KHIDEUX.INVERSE
CHITEST = TEST.KHIDEUX
CONCATENATE = CONCATENER
CONFIDENCE = INTERVALLE.CONFIANCE
COVAR = COVARIANCE
CRITBINOM = CRITERE.LOI.BINOMIALE
EXPONDIST = LOI.EXPONENTIELLE
FDIST = LOI.F
FINV = INVERSE.LOI.F
FLOOR = PLANCHER
FORECAST = PREVISION
FTEST = TEST.F
GAMMADIST = LOI.GAMMA
GAMMAINV = LOI.GAMMA.INVERSE
HYPGEOMDIST = LOI.HYPERGEOMETRIQUE
LOGINV = LOI.LOGNORMALE.INVERSE
LOGNORMDIST = LOI.LOGNORMALE
MODE = MODE
NEGBINOMDIST = LOI.BINOMIALE.NEG
NORMDIST = LOI.NORMALE
NORMINV = LOI.NORMALE.INVERSE
NORMSDIST = LOI.NORMALE.STANDARD
NORMSINV = LOI.NORMALE.STANDARD.INVERSE
PERCENTILE = CENTILE
PERCENTRANK = RANG.POURCENTAGE
POISSON = LOI.POISSON
QUARTILE = QUARTILE
RANK = RANG
STDEV = ECARTYPE
STDEVP = ECARTYPEP
TDIST = LOI.STUDENT
TINV = LOI.STUDENT.INVERSE
TTEST = TEST.STUDENT
VAR = VAR
VARP = VAR.P
WEIBULL = LOI.WEIBULL
ZTEST = TEST.Z
