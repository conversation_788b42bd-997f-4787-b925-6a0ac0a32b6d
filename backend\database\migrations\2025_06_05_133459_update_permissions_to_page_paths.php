<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UpdatePermissionsToPagePaths extends Migration
{
    public function up()
    {
        // List of page paths from frontend PAGE_CATEGORIES
        $pagePaths = [
            'dashboard', 'items', 'expiry', 'store-locations', 'StockReport', 'ItemWiseStockReport',
            'StockRecheck', 'BarcodePage', 'StockTransfer', 'purchasing', 'PurchaseReturn',
            'PurchaseInvoice', 'PurchaseOrder', 'suppliers', 'SupplierForm', 'sales', 'SalesReturn',
            'Customers', 'SalesInvoice', 'quotation', 'DiscountScheam', 'pos', 'touchpos',
            'billPrintModel', 'production', 'MakeProductForm', 'ProductModal',
            'ProductionCategoryModal', 'RawMaterialModal', 'reports', 'ReportTable',
            'DailyProfit', 'BillWiseProfit', 'CompanyWiseProfit', 'SupplierWiseProfit',
            'Outstanding', 'settings', 'CreateCompany', 'CalculatorModal', 'UserList',
            'UserModal', 'RoleList', 'PERMISSIONS', 'RecycleBin', 'StaffManagement',
            'StaffRegistration', 'RoleBasedAccessControl', 'AttendanceShiftManagement',
            'PayrollSalaryManagement', 'Approvels', 'HomePage', 'ProjectsPage', 'TasksPage',
            'SubtasksPage', 'ReportPage', 'ProjectForm', 'SubtaskForm', 'TaskForm',
            'categories', 'CategoryForm', 'units', 'UnitForm'
        ];

        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Store existing role-permission mappings
        $rolePermissions = DB::table('role_has_permissions')
            ->join('permissions', 'role_has_permissions.permission_id', '=', 'permissions.id')
            ->join('roles', 'role_has_permissions.role_id', '=', 'roles.id')
            ->select('roles.id as role_id', 'roles.name as role_name', 'permissions.name as permission_name')
            ->get();

        // Map old permissions to new ones (e.g., 'dashboard.view' to 'dashboard')
        $permissionMap = [];
        foreach ($rolePermissions as $rp) {
            $page = explode('.', $rp->permission_name)[0]; // Extract page from 'page.action'
            if (in_array($page, $pagePaths)) {
                $permissionMap[$rp->role_id][] = $page;
            }
        }

        // Clear related records in Spatie's permission-related tables
        DB::table('model_has_permissions')->truncate();
        DB::table('role_has_permissions')->truncate();
        DB::table('permissions')->truncate();

        // Insert new permissions
        foreach ($pagePaths as $path) {
            Permission::create([
                'name' => $path,
                'guard_name' => 'api',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Reassign permissions to roles
        foreach ($permissionMap as $roleId => $permissions) {
            $role = Role::find($roleId);
            if ($role) {
                $role->syncPermissions(array_unique($permissions));
            }
        }

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Clear permission cache
        Artisan::call('permission:cache-reset');
    }

    public function down()
    {
        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clear related records
        DB::table('model_has_permissions')->truncate();
        DB::table('role_has_permissions')->truncate();
        DB::table('permissions')->truncate();

        // Optionally restore old permissions (example)
        $oldPermissions = [
            ['name' => 'dashboard.view', 'guard_name' => 'api', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'dashboard.create', 'guard_name' => 'api', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'items.view', 'guard_name' => 'api', 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'items.create', 'guard_name' => 'api', 'created_at' => now(), 'updated_at' => now()],
            // Add other old permissions as needed
        ];
        DB::table('permissions')->insert($oldPermissions);

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Clear permission cache
        Artisan::call('permission:cache-reset');
    }
}