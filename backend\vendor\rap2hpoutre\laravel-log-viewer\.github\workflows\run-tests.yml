name: Tests

on:
  push:
  pull_request:

jobs:
  tests:
    strategy:
      fail-fast: true
      matrix:
        php: ["7.2", "7.4", "8.0", "8.1", "8.2", "8.3", "8.4"]
        laravel: ["^6.0", "^7.0", "^8.0", "^9.0", "^10.0", "^11.0", "^12.0"]
        exclude:
          - php: "8.0"
            laravel: "^10.0"
          - php: "7.4"
            laravel: "^10.0"
          - php: "7.2"
            laravel: "^10.0"
          - php: "7.4"
            laravel: "^9.0"
          - php: "7.2"
            laravel: "^9.0"
          - php: "8.4"
            laravel: "^8.0"
          - php: "8.3"
            laravel: "^8.0"
          - php: "8.2"
            laravel: "^8.0"
          - php: "7.2"
            laravel: "^8.0"
          - php: "8.4"
            laravel: "^7.0"
          - php: "8.3"
            laravel: "^7.0"
          - php: "8.2"
            laravel: "^7.0"
          - php: "8.1"
            laravel: "^7.0"
          - php: "8.4"
            laravel: "^6.0"
          - php: "8.3"
            laravel: "^6.0"
          - php: "8.2"
            laravel: "^6.0"
          - php: "8.1"
            laravel: "^6.0"
          - php: "7.2"
            laravel: "^11.0"
          - php: "7.4"
            laravel: "^11.0"
          - php: "8.0"
            laravel: "^11.0"
          - php: "8.1"
            laravel: "^11.0"
          - php: "7.2"
            laravel: "^12.0"
          - php: "7.4"
            laravel: "^12.0"
          - php: "8.0"
            laravel: "^12.0"
          - php: "8.1"
            laravel: "^12.0"
    name: "PHP${{ matrix.php }} - Laravel${{ matrix.laravel }}"

    runs-on: "ubuntu-latest"

    steps:
      - name: "Checkout code"
        uses: "actions/checkout@v3"

      - name: "Setup PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php }}"
          extensions: "dom, curl, libxml, mbstring, zip, fileinfo"
          tools: "composer:v2"
          coverage: "none"

      - name: "Check Composer configuration"
        run: "composer validate --strict"

      - name: "Install dependencies from composer.json"
        run: "composer update --with='laravel/framework:${{ matrix.laravel }}' --no-interaction --no-progress"

      - name: "Check PSR-4 mapping"
        run: "composer dump-autoload --optimize --strict-psr"

      - name: "Execute unit tests"
        run: "vendor/bin/phpunit"
