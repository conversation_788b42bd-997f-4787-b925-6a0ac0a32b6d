<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SalesReturn;
use Illuminate\Http\JsonResponse;

class SalesReturnNextNumberController extends Controller
{
    /**
     * Get the next sales return number.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNextNumber(): JsonResponse
    {
        $nextNumber = SalesReturn::generateSalesReturnNumber();
        return response()->json(['next_sales_return_number' => $nextNumber]);
    }
}
