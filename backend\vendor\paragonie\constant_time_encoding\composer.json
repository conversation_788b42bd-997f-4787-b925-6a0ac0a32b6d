{"name": "paragonie/constant_time_encoding", "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base64", "encoding", "rfc4648", "base32", "base16", "hex", "bin2hex", "hex2bin", "base64_encode", "base64_decode", "base32_encode", "base32_decode"], "license": "MIT", "type": "library", "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "support": {"issues": "https://github.com/paragonie/constant_time_encoding/issues", "email": "<EMAIL>", "source": "https://github.com/paragonie/constant_time_encoding"}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "autoload-dev": {"psr-4": {"ParagonIE\\ConstantTime\\Tests\\": "tests/"}}}