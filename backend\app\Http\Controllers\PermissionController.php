<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Log;
use Artisan;

class PermissionController extends Controller
{
    public function index()
    {
        $permissions = Permission::all()->map(function ($permission) {
            return [
                'id' => $permission->id,
                'name' => $permission->name,
                'description' => $permission->description,
            ];
        });
        return response()->json($permissions);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|unique:permissions,name', // e.g., 'dashboard', 'items'
            'description' => 'nullable|string'
        ]);

        $permission = Permission::create([
            'name' => $request->name,
            'description' => $request->description,
            'guard_name' => 'api'
        ]);

        Log::info('Created permission: ' . $request->name);
        Artisan::call('permission:cache-reset');
        Log::info('Permission cache cleared after creating permission: ' . $request->name);

        return response()->json($permission, 201);
    }

    public function show(Permission $permission)
    {
        return response()->json([
            'id' => $permission->id,
            'name' => $permission->name,
            'description' => $permission->description,
        ]);
    }

    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => 'sometimes|string|unique:permissions,name,' . $permission->id,
            'description' => 'nullable|string'
        ]);

        $permission->update($request->only(['name', 'description']));
        Artisan::call('permission:cache-reset');
        Log::info('Permission cache cleared after updating permission: ' . $permission->name);

        return response()->json($permission);
    }

    public function destroy(Permission $permission)
    {
        $name = $permission->name;
        $permission->delete();
        Artisan::call('permission:cache-reset');
        Log::info('Permission cache cleared after deleting permission: ' . $name);

        return response()->noContent();
    }
}