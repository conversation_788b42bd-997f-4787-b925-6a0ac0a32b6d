# ChangeLog

All notable changes are documented in this file using the [Keep a CHANGELOG](https://keepachangelog.com/) principles.

## [3.0.2] - 2024-07-03

### Changed

* This project now uses PHPStan instead of Psalm for static analysis

## [3.0.1] - 2024-03-02

### Changed

* Do not use implicitly nullable parameters

## [3.0.0] - 2024-02-02

### Removed

* This component is no longer supported on PHP 8.1

## [2.0.1] - 2024-03-02

### Changed

* Do not use implicitly nullable parameters

## [2.0.0] - 2023-02-03

### Removed

* This component is no longer supported on PHP 7.3, PHP 7.4, and PHP 8.0

## [1.0.1] - 2020-09-28

### Changed

* Changed PHP version constraint in `composer.json` from `^7.3 || ^8.0` to `>=7.3`

## [1.0.0] - 2020-08-12

* Initial release

[3.0.2]: https://github.com/sebastian<PERSON>mann/cli-parser/compare/3.0.1...3.0.2
[3.0.1]: https://github.com/sebastianbergmann/cli-parser/compare/3.0.0...3.0.1
[3.0.0]: https://github.com/sebastianbergmann/cli-parser/compare/2.0...3.0.0
[2.0.1]: https://github.com/sebastianbergmann/cli-parser/compare/2.0.0...2.0.1
[2.0.0]: https://github.com/sebastianbergmann/cli-parser/compare/1.0.1...2.0.0
[1.0.1]: https://github.com/sebastianbergmann/cli-parser/compare/1.0.0...1.0.1
[1.0.0]: https://github.com/sebastianbergmann/cli-parser/compare/bb7bb3297957927962b0a3335befe7b66f7462e9...1.0.0
