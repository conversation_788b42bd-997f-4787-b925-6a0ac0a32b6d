<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropUnwantedApprovalColumnsFromQuotations extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('quotations', function (Blueprint $table) {
            if (Schema::hasColumn('quotations', 'received_by')) {
                $table->dropColumn('received_by');
            }
            if (Schema::hasColumn('quotations', 'checked_by')) {
                $table->dropColumn('checked_by');
            }
            if (Schema::hasColumn('quotations', 'authorized_by')) {
                $table->dropColumn('authorized_by');
            }
            if (Schema::hasColumn('quotations', 'approved')) {
                $table->dropColumn('approved');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('quotations', function (Blueprint $table) {
            $table->string('received_by')->nullable();
            $table->string('checked_by')->nullable();
            $table->string('authorized_by')->nullable();
            $table->string('approved')->nullable();
        });
    }
}
