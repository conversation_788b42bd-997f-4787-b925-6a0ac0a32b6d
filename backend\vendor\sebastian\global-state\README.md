[![Latest Stable Version](https://poser.pugx.org/sebastian/global-state/v/stable.png)](https://packagist.org/packages/sebastian/global-state)
[![CI Status](https://github.com/sebastianbergmann/global-state/workflows/CI/badge.svg)](https://github.com/sebastianbergmann/global-state/actions)
[![codecov](https://codecov.io/gh/sebas<PERSON><PERSON><PERSON>/global-state/branch/main/graph/badge.svg)](https://codecov.io/gh/sebastianbergmann/global-state)

# sebastian/global-state

Snapshotting of global state, factored out of PHPUnit into a stand-alone component.

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

```
composer require sebastian/global-state
```

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

```
composer require --dev sebastian/global-state
```
