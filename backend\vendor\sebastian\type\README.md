[![Latest Stable Version](https://poser.pugx.org/sebastian/type/v)](https://packagist.org/packages/sebastian/type)
[![CI Status](https://github.com/sebastianbergmann/type/workflows/CI/badge.svg)](https://github.com/sebastian<PERSON>mann/type/actions)
[![codecov](https://codecov.io/gh/sebastian<PERSON>mann/type/branch/main/graph/badge.svg)](https://codecov.io/gh/sebastianbergmann/type)

# sebastian/type

Collection of value objects that represent the types of the PHP type system.

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

```
composer require sebastian/type
```

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

```
composer require --dev sebastian/type
```
