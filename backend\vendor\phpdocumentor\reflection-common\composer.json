{"name": "phpdocumentor/reflection-common", "keywords": ["phpdoc", "phpDocumentor", "reflection", "static analysis", "FQSEN"], "homepage": "http://www.phpdoc.org", "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.2 || ^8.0"}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "require-dev": {}, "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}}