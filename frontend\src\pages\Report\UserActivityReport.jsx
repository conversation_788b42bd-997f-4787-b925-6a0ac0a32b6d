import React, { useState, useEffect, useMemo } from 'react';
import axios from 'axios';
import { 
  FiSearch, 
  FiFilter, 
  FiDownload, 
  FiRefreshCw, 
  FiUser, 
  FiClock, 
  FiMapPin, 
  FiActivity,
  FiCalendar,
  FiEye,
  FiEyeOff,
  FiChevronDown,
  FiChevronRight,
  FiInfo,
  FiDatabase,
  FiHash,
  FiMonitor
} from 'react-icons/fi';
import { useAuth } from '../../context/NewAuthContext';

const UserActivityReport = () => {
  // State management
  const [activities, setActivities] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expandedRows, setExpandedRows] = useState(new Set());
  
  // Filter states
  const [selectedUser, setSelectedUser] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [actionFilter, setActionFilter] = useState('');
  const [moduleFilter, setModuleFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  
  const { user: currentUser } = useAuth();

  // Fetch users for filter dropdown
  const fetchUsers = async () => {
    try {
      const response = await axios.get('/api/users', {
        headers: { Authorization: `Bearer ${currentUser.token}` }
      });
      setUsers(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch users:', error);
    }
  };

  // Fetch activity logs
  const fetchActivityLogs = async () => {
    setLoading(true);
    setError(null);
    
    try {
      let url = '/api/user-activity-logs';
      const params = new URLSearchParams();
      
      if (selectedUser) params.append('user_id', selectedUser);
      if (dateRange.startDate) params.append('start_date', dateRange.startDate);
      if (dateRange.endDate) params.append('end_date', dateRange.endDate);
      if (actionFilter) params.append('action', actionFilter);
      if (moduleFilter) params.append('module', moduleFilter);
      if (searchTerm) params.append('search', searchTerm);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await axios.get(url, {
        headers: { Authorization: `Bearer ${currentUser.token}` }
      });
      
      setActivities(response.data.data || []);
    } catch (error) {
      setError('Failed to fetch activity logs');
      console.error('Activity logs fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Effects
  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    fetchActivityLogs();
  }, [selectedUser, dateRange, actionFilter, moduleFilter, searchTerm]);

  // Computed values
  const filteredActivities = useMemo(() => {
    return activities.filter(activity => {
      const matchesSearch = !searchTerm || 
        activity.user_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.action?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.ip_address?.includes(searchTerm) ||
        activity.module?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.details?.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesSearch;
    });
  }, [activities, searchTerm]);

  const paginatedActivities = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredActivities.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredActivities, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredActivities.length / itemsPerPage);

  // Event handlers
  const handleRefresh = () => {
    fetchActivityLogs();
  };

  const handleExport = () => {
    // Export functionality
    const csvContent = generateCSV(filteredActivities);
    downloadCSV(csvContent, 'user-activity-report.csv');
  };

  const handleClearFilters = () => {
    setSelectedUser('');
    setDateRange({ startDate: '', endDate: '' });
    setActionFilter('');
    setModuleFilter('');
    setSearchTerm('');
    setCurrentPage(1);
  };

  const toggleRowExpansion = (activityId) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(activityId)) {
      newExpandedRows.delete(activityId);
    } else {
      newExpandedRows.add(activityId);
    }
    setExpandedRows(newExpandedRows);
  };

  const generateCSV = (data) => {
    const headers = ['User', 'Action', 'Module', 'IP Address', 'Date', 'Time', 'Details', 'Record ID'];
    const rows = data.map(item => [
      item.user_name || 'Unknown',
      item.action || '',
      item.module || '',
      item.ip_address || '',
      new Date(item.created_at).toLocaleDateString(),
      new Date(item.created_at).toLocaleTimeString(),
      item.details ? JSON.stringify(item.details) : '',
      item.record_id || ''
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const downloadCSV = (content, filename) => {
    const blob = new Blob([content], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getActionIcon = (action) => {
    const actionLower = action?.toLowerCase();
    if (actionLower?.includes('login')) return <FiUser className="text-green-500" />;
    if (actionLower?.includes('logout')) return <FiUser className="text-red-500" />;
    if (actionLower?.includes('create')) return <FiActivity className="text-blue-500" />;
    if (actionLower?.includes('update')) return <FiActivity className="text-yellow-500" />;
    if (actionLower?.includes('delete')) return <FiActivity className="text-red-500" />;
    if (actionLower?.includes('restore')) return <FiActivity className="text-purple-500" />;
    return <FiActivity className="text-gray-500" />;
  };

  const getActionColor = (action) => {
    const actionLower = action?.toLowerCase();
    if (actionLower?.includes('login')) return 'text-green-600 bg-green-50';
    if (actionLower?.includes('logout')) return 'text-red-600 bg-red-50';
    if (actionLower?.includes('create')) return 'text-blue-600 bg-blue-50';
    if (actionLower?.includes('update')) return 'text-yellow-600 bg-yellow-50';
    if (actionLower?.includes('delete')) return 'text-red-600 bg-red-50';
    if (actionLower?.includes('restore')) return 'text-purple-600 bg-purple-50';
    return 'text-gray-600 bg-gray-50';
  };

  const formatDetails = (details) => {
    if (!details) return 'No details available';
    
    if (typeof details === 'string') {
      try {
        details = JSON.parse(details);
      } catch (e) {
        return details;
      }
    }
    
    if (typeof details === 'object') {
      return Object.entries(details)
        .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
        .join('<br/>');
    }
    
    return JSON.stringify(details, null, 2);
  };

  const renderDetailsRow = (activity) => {
    if (!expandedRows.has(activity.id)) return null;

    return (
      <tr className="bg-gray-50 dark:bg-gray-700">
        <td colSpan="6" className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Module and Record ID */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <FiDatabase className="text-blue-500" />
                <span className="font-medium text-gray-700 dark:text-gray-300">Module:</span>
                <span className="text-gray-600 dark:text-gray-400">
                  {activity.module || 'N/A'}
                </span>
              </div>
              
              {activity.record_id && (
                <div className="flex items-center space-x-2">
                  <FiHash className="text-green-500" />
                  <span className="font-medium text-gray-700 dark:text-gray-300">Record ID:</span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {activity.record_id}
                  </span>
                </div>
              )}
              
              {activity.user_agent && (
                <div className="flex items-start space-x-2">
                  <FiMonitor className="text-purple-500 mt-1" />
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">User Agent:</span>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1 break-all">
                      {activity.user_agent}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Details */}
            <div className="space-y-3">
              <div className="flex items-start space-x-2">
                <FiInfo className="text-orange-500 mt-1" />
                <div className="flex-1">
                  <span className="font-medium text-gray-700 dark:text-gray-300">Details:</span>
                  <div 
                    className="text-sm text-gray-600 dark:text-gray-400 mt-1"
                    dangerouslySetInnerHTML={{ __html: formatDetails(activity.details) }}
                  />
                </div>
              </div>
              
              {/* Old and New Values */}
              {(activity.old_values || activity.new_values) && (
                <div className="grid grid-cols-2 gap-2">
                  {activity.old_values && (
                    <div>
                      <span className="text-xs font-medium text-gray-500">Old Values:</span>
                      <pre className="text-xs text-gray-600 dark:text-gray-400 mt-1 bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto">
                        {JSON.stringify(activity.old_values, null, 2)}
                      </pre>
                    </div>
                  )}
                  {activity.new_values && (
                    <div>
                      <span className="text-xs font-medium text-gray-500">New Values:</span>
                      <pre className="text-xs text-gray-600 dark:text-gray-400 mt-1 bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto">
                        {JSON.stringify(activity.new_values, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </td>
      </tr>
    );
  };

  // Render methods
  const renderFilters = () => (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* User Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            User
          </label>
          <select
            value={selectedUser}
            onChange={(e) => setSelectedUser(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="">All Users</option>
            {users.map(user => (
              <option key={user.id} value={user.id}>
                {user.name}
              </option>
            ))}
          </select>
        </div>

        {/* Start Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Start Date
          </label>
          <input
            type="date"
            value={dateRange.startDate}
            onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        {/* End Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            End Date
          </label>
          <input
            type="date"
            value={dateRange.endDate}
            onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        {/* Action Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Action
          </label>
          <select
            value={actionFilter}
            onChange={(e) => setActionFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="">All Actions</option>
            <option value="login">Login</option>
            <option value="logout">Logout</option>
            <option value="create">Create</option>
            <option value="update">Update</option>
            <option value="delete">Delete</option>
            <option value="restore">Restore</option>
          </select>
        </div>

        {/* Module Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Module
          </label>
          <select
            value={moduleFilter}
            onChange={(e) => setModuleFilter(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="">All Modules</option>
            <option value="Authentication">Authentication</option>
            <option value="Products">Products</option>
            <option value="Customers">Customers</option>
            <option value="Sales">Sales</option>
            <option value="Sales Returns">Sales Returns</option>
            <option value="Purchases">Purchases</option>
            <option value="Purchase Returns">Purchase Returns</option>
            <option value="Users">Users</option>
          </select>
        </div>
      </div>

      <div className="flex justify-between items-center mt-4">
        <button
          onClick={handleClearFilters}
          className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
        >
          Clear Filters
        </button>
        
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {filteredActivities.length} activities found
        </div>
      </div>
    </div>
  );

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="flex justify-center items-center space-x-2 mt-6">
        <button
          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
          disabled={currentPage === 1}
          className="px-3 py-2 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          Previous
        </button>

        {pages.map(page => (
          <button
            key={page}
            onClick={() => setCurrentPage(page)}
            className={`px-3 py-2 text-sm border rounded-md ${
              currentPage === page
                ? 'bg-blue-500 text-white border-blue-500'
                : 'hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
          >
            {page}
          </button>
        ))}

        <button
          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
          disabled={currentPage === totalPages}
          className="px-3 py-2 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          Next
        </button>
      </div>
    );
  };

  // Main render
  return (
    <div className="min-h-screen p-4 md:p-8 bg-gradient-to-br from-blue-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
              User Activity Report
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Monitor and track user activities across the system with detailed information
            </p>
          </div>
          
          <div className="flex items-center space-x-3 mt-4 md:mt-0">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
            >
              <FiFilter className="mr-2" />
              {showFilters ? <FiEyeOff className="mr-2" /> : <FiEye className="mr-2" />}
              Filters
            </button>
            
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors"
            >
              <FiRefreshCw className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            
            <button
              onClick={handleExport}
              disabled={filteredActivities.length === 0}
              className="flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 transition-colors"
            >
              <FiDownload className="mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search by user name, action, module, IP address, or details..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>
        </div>

        {/* Filters */}
        {showFilters && renderFilters()}

        {/* Error Display */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <strong>Error:</strong> {error}
          </div>
        )}

        {/* Activity Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Action
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Module
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    IP Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Details
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {loading ? (
                  <tr>
                    <td colSpan="6" className="px-6 py-4 text-center">
                      <div className="flex items-center justify-center">
                        <FiRefreshCw className="animate-spin mr-2" />
                        Loading activities...
                      </div>
                    </td>
                  </tr>
                ) : paginatedActivities.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                      <FiActivity className="mx-auto h-12 w-12 mb-4 opacity-50" />
                      <p>No activities found</p>
                      <p className="text-sm">Try adjusting your filters or search terms</p>
                    </td>
                  </tr>
                ) : (
                  paginatedActivities.map((activity, index) => (
                    <React.Fragment key={activity.id || index}>
                      <tr className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
                              <FiUser className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                            </div>
                            <div className="ml-3">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {activity.user_name || 'Unknown User'}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {activity.user_email || 'No email'}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {getActionIcon(activity.action)}
                            <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getActionColor(activity.action)}`}>
                              {activity.action || 'Unknown Action'}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-900 dark:text-white">
                            <FiDatabase className="mr-1 text-gray-400" />
                            {activity.module || 'N/A'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-900 dark:text-white">
                            <FiMapPin className="mr-1 text-gray-400" />
                            {activity.ip_address || 'N/A'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-900 dark:text-white">
                            <FiClock className="mr-1 text-gray-400" />
                            <div>
                              <div>{new Date(activity.created_at).toLocaleDateString()}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {new Date(activity.created_at).toLocaleTimeString()}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => toggleRowExpansion(activity.id)}
                            className="flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          >
                            {expandedRows.has(activity.id) ? (
                              <FiChevronDown className="mr-1" />
                            ) : (
                              <FiChevronRight className="mr-1" />
                            )}
                            {expandedRows.has(activity.id) ? 'Hide' : 'View'} Details
                          </button>
                        </td>
                      </tr>
                      {renderDetailsRow(activity)}
                    </React.Fragment>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        {renderPagination()}

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <div className="flex items-center">
              <FiUser className="h-8 w-8 text-blue-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Users</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">{users.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <div className="flex items-center">
              <FiActivity className="h-8 w-8 text-green-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Activities</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">{filteredActivities.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <div className="flex items-center">
              <FiCalendar className="h-8 w-8 text-purple-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Today's Activities</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {filteredActivities.filter(activity => 
                    new Date(activity.created_at).toDateString() === new Date().toDateString()
                  ).length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <div className="flex items-center">
              <FiMapPin className="h-8 w-8 text-orange-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Unique IPs</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {new Set(filteredActivities.map(activity => activity.ip_address).filter(Boolean)).size}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserActivityReport;