<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
 
class PurchaseOrder extends Model
{
    protected $fillable = ['supplier_id', 'contact_name', 'phone', 'address', 'total'];

    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'supplier_id');
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class, 'purchase_order_id');
    }
}
