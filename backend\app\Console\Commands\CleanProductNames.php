<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;

class CleanProductNames extends Command
{
    protected $signature = 'clean:product-names';
    protected $description = 'Clean product names by trimming whitespace';

    public function handle()
    {
        $this->info('Cleaning product names...');
        
        $products = Product::all();
        $cleaned = 0;
        
        foreach ($products as $product) {
            $originalName = $product->product_name;
            $trimmedName = trim($originalName);
            
            if ($originalName !== $trimmedName) {
                $this->line("Cleaning: '{$originalName}' -> '{$trimmedName}'");
                $product->product_name = $trimmedName;
                $product->save();
                $cleaned++;
            }
        }
        
        $this->info("Cleaned {$cleaned} product names.");
        
        if ($cleaned > 0) {
            $this->info('Product names have been cleaned. Please test the batch addition again.');
        } else {
            $this->info('No product names needed cleaning.');
        }
    }
}
